import React from 'react';
import styles from './ScrollableContainer.module.css';

const ScrollableContainer = ({ 
  children, 
  className = '', 
  maxHeight = '215px',
  ...props 
}) => {
  const containerClasses = [
    styles.scrollableContainer,
    className
  ].filter(Boolean).join(' ');

  return (
    <div 
      className={containerClasses}
      style={{ maxHeight }}
      {...props}
    >
      {children}
    </div>
  );
};

export default ScrollableContainer;
