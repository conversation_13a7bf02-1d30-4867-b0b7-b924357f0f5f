# ScrollableContainer Component

A reusable container component that provides consistent scrollbar styling across the application.

## Features

- **Custom Scrollbar**: Matches the design system with consistent styling
- **Configurable Height**: Set maximum height for scrollable content
- **Responsive**: Adapts to content while maintaining scroll behavior
- **Consistent Styling**: Same scrollbar appearance as TextField and other components

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `children` | `node` | `undefined` | Content to be rendered inside the scrollable container |
| `className` | `string` | `''` | Additional CSS classes |
| `maxHeight` | `string` | `'215px'` | Maximum height before scrolling |

## Usage

```jsx
import ScrollableContainer from './components/ScrollableContainer/ScrollableContainer';

// Basic usage
<ScrollableContainer>
  <div>Long content that will scroll...</div>
  <div>More content...</div>
  <div>Even more content...</div>
</ScrollableContainer>

// Custom height
<ScrollableContainer maxHeight="300px">
  <div>Content with custom max height...</div>
</ScrollableContainer>

// With custom styling
<ScrollableContainer className="custom-scroll-area" maxHeight="150px">
  <div>Custom styled scrollable content...</div>
</ScrollableContainer>
```

## Design Specifications

- **Scrollbar Width**: 8px
- **Track**: Transparent background
- **Thumb**: Gray color (#383E46) with 4px border radius
- **Thumb Hover**: Light gray color (#C8CDD5)
- **Overflow**: Vertical scroll, horizontal hidden

## Scrollbar Styling

The component uses the same scrollbar styling as the TextField component:

```css
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--UI-uiLines01, #383E46);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--UI-uiInteractive02, #C8CDD5);
}
```

## Use Cases

- **SelectMenu**: Scrollable menu items list
- **Dropdown Lists**: Any dropdown with many options
- **Content Areas**: Scrollable content sections
- **Modal Content**: Scrollable modal bodies
- **Sidebar Content**: Scrollable navigation or content areas

This component ensures consistent scrollbar appearance across all scrollable elements in the application.
