import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import Checkbox from './Checkbox';

describe('Checkbox Component', () => {
  test('renders with default props', () => {
    render(<Checkbox />);
    const checkbox = screen.getByRole('checkbox');
    const label = screen.getByText('Checkbox Label');
    
    expect(checkbox).toBeInTheDocument();
    expect(label).toBeInTheDocument();
    expect(checkbox).not.toBeChecked();
    expect(checkbox).not.toBeDisabled();
  });

  test('renders without label when showLabel is false', () => {
    render(<Checkbox showLabel={false} />);
    const checkbox = screen.getByRole('checkbox');
    const label = screen.queryByText('Checkbox Label');
    
    expect(checkbox).toBeInTheDocument();
    expect(label).not.toBeInTheDocument();
  });

  test('renders with custom label', () => {
    render(<Checkbox label="Custom Label" />);
    const label = screen.getByText('Custom Label');
    
    expect(label).toBeInTheDocument();
  });

  test('handles checked state', () => {
    render(<Checkbox checked={true} />);
    const checkbox = screen.getByRole('checkbox');
    
    expect(checkbox).toBeChecked();
  });

  test('handles disabled state', () => {
    render(<Checkbox disabled={true} />);
    const checkbox = screen.getByRole('checkbox');
    
    expect(checkbox).toBeDisabled();
  });

  test('handles indeterminate state', () => {
    render(<Checkbox indeterminate={true} />);
    const checkbox = screen.getByRole('checkbox');
    
    expect(checkbox.indeterminate).toBe(true);
  });

  test('calls onChange when clicked', () => {
    const handleChange = jest.fn();
    render(<Checkbox onChange={handleChange} />);
    const checkbox = screen.getByRole('checkbox');
    
    fireEvent.click(checkbox);
    
    expect(handleChange).toHaveBeenCalledTimes(1);
  });

  test('does not call onChange when disabled', () => {
    const handleChange = jest.fn();
    render(<Checkbox disabled={true} onChange={handleChange} />);
    const checkbox = screen.getByRole('checkbox');
    
    fireEvent.click(checkbox);
    
    expect(handleChange).not.toHaveBeenCalled();
  });

  test('applies custom className', () => {
    render(<Checkbox className="custom-class" />);
    const container = screen.getByRole('checkbox').closest('label');
    
    expect(container).toHaveClass('custom-class');
  });

  test('handles focus and blur events', () => {
    render(<Checkbox />);
    const checkbox = screen.getByRole('checkbox');
    
    fireEvent.focus(checkbox);
    // Focus state is handled internally, just ensure no errors
    
    fireEvent.blur(checkbox);
    // Blur state is handled internally, just ensure no errors
  });

  test('handles mouse enter and leave events', () => {
    render(<Checkbox />);
    const container = screen.getByRole('checkbox').closest('label');

    fireEvent.mouseEnter(container);
    // Hover state is handled internally, just ensure no errors

    fireEvent.mouseLeave(container);
    // Hover state is handled internally, just ensure no errors
  });

  test('indeterminate checkbox always goes to checked when clicked', () => {
    const handleChange = jest.fn();
    render(<Checkbox indeterminate={true} checked={false} onChange={handleChange} />);
    const checkbox = screen.getByRole('checkbox');

    fireEvent.click(checkbox);

    expect(handleChange).toHaveBeenCalledTimes(1);
    expect(handleChange).toHaveBeenCalledWith(
      expect.objectContaining({
        target: expect.objectContaining({
          checked: true
        })
      })
    );
  });

  test('indeterminate checkbox goes to checked even when already checked', () => {
    const handleChange = jest.fn();
    render(<Checkbox indeterminate={true} checked={true} onChange={handleChange} />);
    const checkbox = screen.getByRole('checkbox');

    fireEvent.click(checkbox);

    expect(handleChange).toHaveBeenCalledTimes(1);
    expect(handleChange).toHaveBeenCalledWith(
      expect.objectContaining({
        target: expect.objectContaining({
          checked: true
        })
      })
    );
  });
});
