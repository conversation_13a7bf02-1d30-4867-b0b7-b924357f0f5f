# Checkbox Component

A comprehensive checkbox component with 9 different states and optional labels, following the Figma design specifications.

## Features

- **9 States**: default, hover, focused/pressed, selected, selected-hover/selected-pressed, disabled, disabled-selected, partial selection, and partial selection hover
- **Optional Labels**: Toggle label visibility with `show<PERSON>abel` prop
- **Accessibility**: Proper ARIA attributes and keyboard navigation
- **Controlled Component**: Fully controlled with `checked` and `onChange` props
- **Indeterminate State**: Support for partial selection state with proper click behavior
- **Smart Click Handling**: Indeterminate checkboxes always go to checked state when clicked
- **Disabled State**: Full disabled functionality

## Usage

### Basic Usage

```jsx
import Checkbox from './components/Checkbox/Checkbox';

function MyComponent() {
  const [isChecked, setIsChecked] = useState(false);

  return (
    <Checkbox
      label="Accept terms and conditions"
      checked={isChecked}
      onChange={(e) => setIsChecked(e.target.checked)}
    />
  );
}
```

### Without Label

```jsx
<Checkbox
  showLabel={false}
  checked={isChecked}
  onChange={(e) => setIsChecked(e.target.checked)}
/>
```

### Disabled State

```jsx
<Checkbox
  label="Disabled checkbox"
  checked={true}
  disabled={true}
/>
```

### Indeterminate/Partial State

The indeterminate state is typically used for "Select All" functionality in tables or groups. When clicked, an indeterminate checkbox always goes to the checked state first.

```jsx
// Table/Group select all example
const selectedCount = items.filter(item => item.selected).length;
const isAllSelected = selectedCount === items.length;
const isIndeterminate = selectedCount > 0 && selectedCount < items.length;

<Checkbox
  label="Select All"
  checked={isAllSelected}
  indeterminate={isIndeterminate}
  onChange={(e) => {
    const newSelected = e.target.checked;
    setItems(items => items.map(item => ({ ...item, selected: newSelected })));
  }}
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `checked` | `boolean` | `false` | Controls the checked state |
| `indeterminate` | `boolean` | `false` | Shows partial selection state |
| `disabled` | `boolean` | `false` | Disables the checkbox |
| `showLabel` | `boolean` | `true` | Shows/hides the label text |
| `label` | `string` | `"Checkbox Label"` | Label text content |
| `onChange` | `function` | - | Change event handler |
| `className` | `string` | `""` | Additional CSS classes |
| `id` | `string` | - | HTML id attribute |

## States

1. **Default**: Unchecked, normal state
2. **Hover**: Mouse hover over unchecked checkbox
3. **Focused/Pressed**: Keyboard focus or mouse press
4. **Selected**: Checked state with checkmark
5. **Selected Hover**: Mouse hover over checked checkbox
6. **Selected Pressed**: Keyboard focus or mouse press on checked
7. **Disabled**: Disabled unchecked state
8. **Disabled Selected**: Disabled checked state
9. **Partial Selection**: Indeterminate state with minus icon
10. **Partial Selection Hover**: Mouse hover over indeterminate state

## Click Behavior

- **Normal checkboxes**: Toggle between unchecked ↔ checked
- **Indeterminate checkboxes**: Always go to checked state when clicked
- **Click cycle**: Indeterminate → Checked → Unchecked → Checked → ...
- **Never cycles back to indeterminate**: The indeterminate state is controlled programmatically by the parent component

## Styling

The component uses CSS modules with design tokens from the global styles:

- **Colors**: Uses `--ui-interactive-01`, `--ui-interactive-02`, etc.
- **Typography**: Amazon Ember font, 14px/400 weight
- **Layout**: 24px height container, 12px checkbox size (consistent across all states)
- **Spacing**: 8px gap between checkbox and label
- **Consistency**: Fixed sizing ensures no movement when transitioning between states
- **Seamless Transitions**: Uses transparent borders to eliminate jitter during state changes

## Accessibility

- Proper `label` element association
- Keyboard navigation support
- ARIA attributes for screen readers
- Focus management
- Disabled state handling
