@import '../../styles/LUMINOUS_global_styles.css';

/* Base checkbox container styles */
.checkboxContainer {
  display: inline-flex;
  height: 24px;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  cursor: pointer;
  transition: all 0.2s ease;
}

/* Checkbox shape container */
.checkboxShapeContainer {
  display: flex;
  width: 12px;
  height: 12px;
  padding: var(--Spacing-spacing00, 0px);
  justify-content: center;
  align-items: center;
  gap: 10px;
}

/* Hide the native input */
.hiddenInput {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
  margin: 0;
  padding: 0;
  border: none;
  outline: none;
}

/* Base checkbox shape */
.checkboxShape {
  width: 10px;
  height: 10px;
  border-radius: 2px;
  border: 1px solid var(--ui-interactive-02, #C8CDD5);
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.2s ease;
  position: relative;
}

/* Partial icon (minus) */
.partialIcon {
  width: 8px;
  height: 2px;
  flex-shrink: 0;
  border-radius: 2px;
  background: var(--ui-02, #1F252B);
}

/* Base text styling */
.checkboxText {
  color: var(--text-02, #C8CDD5);
  font-feature-settings: 'liga' off, 'clig' off;
  font-family: "Amazon Ember";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 142.857% */
}

/* STATE: Default */
.checkboxContainer.default .checkboxShape {
  border: 1px solid var(--ui-interactive-02, #C8CDD5);
  background: transparent;
}

/* STATE: Hover */
.checkboxContainer.hover .checkboxShape {
  border: 1px solid var(--ui-interactive-02-hover, #EBECEE);
  background: transparent;
}

/* STATE: Focused/Pressed */
.checkboxContainer.focused .checkboxShape {
  border: 1px solid var(--ui-interactive-02-hover, #EBECEE);
  background: transparent;
}

/* STATE: Selected */
.checkboxContainer.selected .checkboxShape {
  width: 10px;
  height: 10px;
  border: 1px solid transparent;
  background: var(--ui-interactive-01, #78AEFF);
}

/* STATE: Selected Hover */
.checkboxContainer.selected-hover .checkboxShape {
  width: 10px;
  height: 10px;
  border: 1px solid transparent;
  background: var(--ui-interactive-01-hover, #4F96FF);
}

/* STATE: Selected Pressed */
.checkboxContainer.selected-pressed .checkboxShape {
  width: 10px;
  height: 10px;
  border: 1px solid transparent;
  background: var(--ui-interactive-01-hover, #4F96FF);
}

/* STATE: Disabled */
.checkboxContainer.disabled {
  cursor: not-allowed;
}

.checkboxContainer.disabled .checkboxShape {
  border: 1px solid var(--ui-interactive-disabled, #505760);
  background: transparent;
}

.checkboxContainer.disabled .checkboxText {
  color: var(--text-interactive-disabled, #505760);
}

/* STATE: Disabled Selected */
.checkboxContainer.disabled-selected {
  cursor: not-allowed;
}

.checkboxContainer.disabled-selected .checkboxShape {
  width: 10px;
  height: 10px;
  border: 1px solid transparent;
  opacity: 0.5;
  background: var(--ui-interactive-01, #78AEFF);
}

.checkboxContainer.disabled-selected .checkboxText {
  color: var(--text-interactive-disabled, #505760);
}

/* STATE: Partial Selection */
.checkboxContainer.partial .checkboxShape {
  width: 10px;
  height: 10px;
  border: 1px solid transparent;
  background: var(--ui-interactive-01, #78AEFF);
}

/* STATE: Partial Selection Hover */
.checkboxContainer.partial-hover .checkboxShape {
  width: 10px;
  height: 10px;
  border: 1px solid transparent;
  background: var(--ui-interactive-01-hover, #4F96FF);
}

/* STATE: Disabled Partial */
.checkboxContainer.disabled-partial {
  cursor: not-allowed;
}

.checkboxContainer.disabled-partial .checkboxShape {
  width: 10px;
  height: 10px;
  border: 1px solid transparent;
  opacity: 0.5;
  background: var(--ui-interactive-01, #78AEFF);
}

.checkboxContainer.disabled-partial .checkboxText {
  color: var(--text-interactive-disabled, #505760);
}
