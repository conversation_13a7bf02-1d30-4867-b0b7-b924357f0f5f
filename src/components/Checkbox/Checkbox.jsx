import { useState, useRef, useEffect } from 'react';
import styles from './Checkbox.module.css';

// Checkmark SVG Icon
const CheckmarkIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
    <path d="M7.95724 1.21557C8.29388 0.777937 8.92185 0.695575 9.35959 1.03197C9.79721 1.36861 9.87958 1.99658 9.54318 2.43432L4.73361 8.68724C4.24555 9.32172 3.29497 9.34015 2.78244 8.72533L0.481656 5.96557C0.128118 5.54132 0.185396 4.90996 0.609586 4.55639C1.03383 4.20285 1.66519 4.26013 2.01877 4.68432L3.71798 6.72435L7.95724 1.21557Z" fill="#2A3038"/>
  </svg>
);

// Partial/Minus Icon
const PartialIcon = () => (
  <div className={styles.partialIcon} />
);

const Checkbox = ({
  checked = false,
  indeterminate = false,
  disabled = false,
  showLabel = true,
  label = "Checkbox Label",
  onChange,
  className = "",
  id,
  ...props
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const checkboxRef = useRef(null);

  // Handle indeterminate state
  useEffect(() => {
    if (checkboxRef.current) {
      checkboxRef.current.indeterminate = indeterminate;
    }
  }, [indeterminate]);

  const handleChange = (e) => {
    if (!disabled && onChange) {
      // If currently indeterminate, always go to checked state first
      if (indeterminate) {
        // Create a synthetic event with checked = true
        const syntheticEvent = {
          ...e,
          target: {
            ...e.target,
            checked: true
          }
        };
        onChange(syntheticEvent);
      } else {
        // Normal toggle behavior for non-indeterminate states
        onChange(e);
      }
    }
  };

  const handleMouseEnter = () => {
    if (!disabled) {
      setIsHovered(true);
    }
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  const handleFocus = () => {
    if (!disabled) {
      setIsFocused(true);
    }
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  // Determine the current state for styling
  const getState = () => {
    if (disabled && indeterminate) return 'disabled-partial';
    if (disabled && checked) return 'disabled-selected';
    if (disabled) return 'disabled';
    if (indeterminate && isHovered) return 'partial-hover';
    if (indeterminate) return 'partial';
    if (checked && isFocused) return 'selected-pressed';
    if (checked && isHovered) return 'selected-hover';
    if (checked) return 'selected';
    if (isFocused) return 'focused';
    if (isHovered) return 'hover';
    return 'default';
  };

  const currentState = getState();

  // Build container classes
  const containerClasses = [
    styles.checkboxContainer,
    styles[currentState],
    className
  ].filter(Boolean).join(' ');

  // Render the appropriate icon based on state
  const renderIcon = () => {
    if (indeterminate) {
      return <PartialIcon />;
    }
    if (checked) {
      return <CheckmarkIcon />;
    }
    return null;
  };

  return (
    <label 
      className={containerClasses}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className={styles.checkboxShapeContainer}>
        <input
          ref={checkboxRef}
          type="checkbox"
          checked={checked}
          disabled={disabled}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          className={styles.hiddenInput}
          id={id}
          {...props}
        />
        <div className={styles.checkboxShape}>
          {renderIcon()}
        </div>
      </div>
      {showLabel && (
        <span className={styles.checkboxText}>
          {label}
        </span>
      )}
    </label>
  );
};

export default Checkbox;
