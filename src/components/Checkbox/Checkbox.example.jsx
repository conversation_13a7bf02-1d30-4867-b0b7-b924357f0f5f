import React, { useState } from 'react';
import Checkbox from './Checkbox';

const CheckboxExample = () => {
  const [basicChecked, setBasicChecked] = useState(false);
  const [selectedChecked, setSelectedChecked] = useState(true);
  const [partialChecked, setPartialChecked] = useState(false);
  const [disabledSelectedChecked, setDisabledSelectedChecked] = useState(true);

  return (
    <div style={{ padding: '20px', display: 'flex', flexDirection: 'column', gap: '20px' }}>
      <h2>Checkbox Component Examples</h2>
      
      <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
        <h3>Interactive States</h3>
        
        <Checkbox
          label="Default State"
          checked={basicChecked}
          onChange={(e) => setBasicChecked(e.target.checked)}
        />
        
        <Checkbox
          label="Selected State"
          checked={selectedChecked}
          onChange={(e) => setSelectedChecked(e.target.checked)}
        />
        
        <Checkbox
          label="Partial Selection"
          checked={partialChecked}
          indeterminate={true}
          onChange={(e) => setPartialChecked(e.target.checked)}
        />
      </div>

      <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
        <h3>Disabled States</h3>
        
        <Checkbox
          label="Disabled Default"
          checked={false}
          disabled={true}
        />
        
        <Checkbox
          label="Disabled Selected"
          checked={disabledSelectedChecked}
          disabled={true}
        />
        
        <Checkbox
          label="Disabled Partial"
          checked={false}
          indeterminate={true}
          disabled={true}
        />
      </div>

      <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
        <h3>Without Labels</h3>
        
        <Checkbox
          showLabel={false}
          checked={false}
          onChange={() => {}}
        />
        
        <Checkbox
          showLabel={false}
          checked={true}
          onChange={() => {}}
        />
        
        <Checkbox
          showLabel={false}
          checked={false}
          indeterminate={true}
          onChange={() => {}}
        />
      </div>

      <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
        <h3>Custom Labels</h3>
        
        <Checkbox
          label="Accept terms and conditions"
          checked={false}
          onChange={() => {}}
        />
        
        <Checkbox
          label="Subscribe to newsletter"
          checked={true}
          onChange={() => {}}
        />
        
        <Checkbox
          label="Enable notifications"
          checked={false}
          indeterminate={true}
          onChange={() => {}}
        />
      </div>
    </div>
  );
};

export default CheckboxExample;
