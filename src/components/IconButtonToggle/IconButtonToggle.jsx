import React, { useState } from 'react';
import styles from './IconButtonToggle.module.css';

const IconButtonToggle = ({ 
  icons = [], 
  layout = 'horizontal', // 'horizontal' or 'vertical'
  selectedIndex = 0,
  onChange
}) => {
  const [selected, setSelected] = useState(selectedIndex);
  const [hoveredIndex, setHoveredIndex] = useState(null);
  const [pressedIndex, setPressedIndex] = useState(null);

  const handleClick = (index) => {
    setSelected(index);
    if (onChange) {
      onChange(index);
    }
  };

  const handleMouseDown = (index) => {
    setPressedIndex(index);
  };

  const handleMouseUp = () => {
    setPressedIndex(null);
  };

  const handleMouseLeave = () => {
    setHoveredIndex(null);
    setPressedIndex(null);
  };

  const getButtonState = (index) => {
    if (selected === index) return 'selected';
    if (pressedIndex === index) return 'pressed';
    if (hoveredIndex === index) return 'hover';
    return 'inactive';
  };

  const renderIcon = (iconComponent, index) => {
    // Clone the icon component (no need to pass state since CSS handles styling)
    if (React.isValidElement(iconComponent)) {
      return iconComponent;
    }

    // If it's a function, call it (no state needed)
    if (typeof iconComponent === 'function') {
      return iconComponent();
    }

    // Fallback to default icon if no valid icon provided
    return <DefaultIcon />;
  };

  const DefaultIcon = () => {
    return (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path d="M16.5 12.75H7.5C7.0875 12.75 6.75 12.4125 6.75 12C6.75 11.5875 7.0875 11.25 7.5 11.25H16.5C16.9125 11.25 17.25 11.5875 17.25 12C17.25 12.4125 16.9125 12.75 16.5 12.75Z"/>
      </svg>
    );
  };

  return (
    <div className={`${styles.container} ${styles[layout]}`}>
      {icons.map((icon, index) => (
        <button
          key={index}
          className={`${styles.iconSlot} ${styles[getButtonState(index)]}`}
          onClick={() => handleClick(index)}
          onMouseEnter={() => setHoveredIndex(index)}
          onMouseLeave={handleMouseLeave}
          onMouseDown={() => handleMouseDown(index)}
          onMouseUp={handleMouseUp}
        >
          <div className={styles.iconContainer}>
            {renderIcon(icon, index)}
          </div>
        </button>
      ))}
    </div>
  );
};

export default IconButtonToggle;
