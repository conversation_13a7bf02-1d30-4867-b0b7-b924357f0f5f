@import '../../styles/LUMINOUS_global_styles.css';

/* Container Styles */
.container {
  display: inline-flex;
  justify-content: center;
  align-items: flex-start;
  border-radius: 4px;
  border: 1px solid var(--UI-uiLines01, #383E46);
  background: var(--UI-ui03, #2A3038);
}

/* Layout Variants */
.horizontal {
  flex-direction: row;
}

.vertical {
  flex-direction: column;
}

/* Icon Slot Styles */
.iconSlot {
  display: inline-flex;
  padding: var(--Spacing-spacing01, 4px);
  justify-content: center;
  align-items: center;
  border-radius: var(--Radius-radius02, 4px);
  background: none;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.15s ease;
}

/* Icon Container */
.iconContainer {
  display: flex;
  width: 24px;
  height: 24px;
  justify-content: center;
  align-items: center;
}

/* State Styles */

/* Inactive State (default) */
.iconSlot.inactive {
  background: none;
  border-color: transparent;
}

/* Hover State */
.iconSlot.hover {
  background: var(--UI-ui02Hover, #0D1114);
  border-color: transparent;
}

/* Pressed State */
.iconSlot.pressed {
  border-color: var(--UI-uiInteractive01, #78AEFF);
  background: var(--UI-ui02Hover, #0D1114);
}

/* Selected State */
.iconSlot.selected {
  background: var(--UI-uiInteractive01, #78AEFF);
  border-color: transparent;
}

/* Focus styles */
.iconSlot:focus {
  outline: none;
}

/* Icon fill colors based on state */
.iconSlot.inactive svg path {
  fill: #C8CDD5;
}

.iconSlot.hover svg path {
  fill: #EBECEE;
}

.iconSlot.pressed svg path {
  fill: #EBECEE;
}

.iconSlot.selected svg path {
  fill: var(--UI-ui01, #0D1114);
}

/* Ensure smooth transitions */
.iconSlot svg path {
  transition: fill 0.15s ease;
}
