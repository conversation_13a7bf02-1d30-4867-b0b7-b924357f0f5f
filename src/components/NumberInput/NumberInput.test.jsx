import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import NumberInput from './NumberInput';

describe('NumberInput', () => {
  it('renders with default props', () => {
    render(<NumberInput />);
    
    expect(screen.getByDisplayValue('0')).toBeInTheDocument();
    expect(screen.getByText('Number')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /generate random number/i })).toBeInTheDocument();
  });

  it('renders with custom label and initial value', () => {
    render(<NumberInput label="Seed" initialValue={42} />);
    
    expect(screen.getByDisplayValue('42')).toBeInTheDocument();
    expect(screen.getByText('Seed')).toBeInTheDocument();
  });

  it('calls onChange when input value changes', () => {
    const handleChange = vi.fn();
    render(<NumberInput onChange={handleChange} />);
    
    const input = screen.getByDisplayValue('0');
    fireEvent.change(input, { target: { value: '25' } });
    
    expect(handleChange).toHaveBeenCalledWith(25);
  });

  it('generates random number when randomize button is clicked', () => {
    const handleChange = vi.fn();
    render(<NumberInput min={1} max={10} onChange={handleChange} />);
    
    const randomizeButton = screen.getByRole('button', { name: /generate random number/i });
    fireEvent.click(randomizeButton);
    
    expect(handleChange).toHaveBeenCalled();
    const calledValue = handleChange.mock.calls[0][0];
    expect(calledValue).toBeGreaterThanOrEqual(1);
    expect(calledValue).toBeLessThanOrEqual(10);
  });

  it('respects min and max constraints', () => {
    render(<NumberInput min={5} max={15} />);
    
    const input = screen.getByDisplayValue('0');
    expect(input).toHaveAttribute('min', '5');
    expect(input).toHaveAttribute('max', '15');
  });

  it('updates display value when randomize is clicked', () => {
    render(<NumberInput min={100} max={100} />); // Fixed range for predictable test
    
    const input = screen.getByDisplayValue('0');
    const randomizeButton = screen.getByRole('button', { name: /generate random number/i });
    
    fireEvent.click(randomizeButton);
    
    expect(input).toHaveValue(100);
  });
});
