# NumberInput Component

A horizontal number input component with a randomize button that allows users to manually input numbers or generate random values within a specified range.

## Features

- **Manual Input**: Users can type numbers directly into the input field
- **Random Generation**: Click the randomize icon to generate a random number within the min/max range
- **Customizable Range**: Set minimum and maximum values for both manual input and random generation
- **Responsive Design**: Follows the design system with proper spacing, typography, and colors
- **Accessibility**: Includes proper ARIA labels and keyboard navigation support

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `label` | `string` | `"Number"` | The label text displayed next to the input |
| `initialValue` | `number` | `0` | The initial value of the input |
| `min` | `number` | `0` | Minimum allowed value |
| `max` | `number` | `100` | Maximum allowed value |
| `onChange` | `function` | `undefined` | Callback function called when the value changes |

## Usage

```jsx
import NumberInput from './components/NumberInput/NumberInput';

// Basic usage
<NumberInput />

// With custom props
<NumberInput
  label="Seed"
  initialValue={42}
  min={0}
  max={999999}
  onChange={(value) => console.log('New value:', value)}
/>

// For AI generation parameters
<NumberInput
  label="Steps"
  initialValue={20}
  min={1}
  max={100}
  onChange={(steps) => setGenerationSteps(steps)}
/>
```

## Design Specifications

- **Container**: 155px width, horizontal flex layout with 8px gap
- **Label**: 12px Amazon Ember Medium, #C8CDD5 color
- **Input Container**: 24px height, 4px border radius, dark background
- **Input Field**: 14px Amazon Ember Regular, white text
- **Icon**: 24x24px randomize icon with hover effects

## Styling

The component uses CSS modules and follows the design system:
- Imports global styles from `LUMINOUS_global_styles.css`
- Uses CSS custom properties for colors and spacing
- Includes hover and focus states for better UX
- Removes default browser number input styling (spin buttons)

## Accessibility

- Proper semantic HTML with `type="number"` input
- ARIA label for the randomize button
- Keyboard accessible (tab navigation, enter to activate button)
- Screen reader friendly with descriptive labels
