@import '../../styles/LUMINOUS_global_styles.css';

/* Number Input container */
.numberInputContainer {
  display: flex;
  width: 155px;
  align-items: center;
  gap: var(--Spacing-spacing02, 8px);
}

/* Label container */
.labelContainer {
  display: flex;
  height: 24px;
  align-items: center;
  gap: var(--Spacing-spacing01, 4px);
}

/* Label (copy) */
.label {
  color: var(--Text-text02, #C8CDD5);
  font-feature-settings: 'liga' off, 'clig' off;
  
  /* Label-1 */
  font-family: "Amazon Ember";
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 16px; /* 133.333% */
}

/* Number input/text input and icon container */
.inputContainer {
  display: flex;
  height: 24px;
  padding: 0px var(--Spacing-spacing01, 4px);
  align-items: center;
  gap: var(--Spacing-spacing00, 0px);
  flex: 1 0 0;

  border-radius: 4px;
  border: 1px solid var(--UI-uiLines01, #383E46);
  background: var(--UI-ui03, #2A3038);
  transition: border-color 0.2s ease;
}

.inputContainer:focus-within {
  border: 1px solid var(--ui-interactive-01, #78AEFF);
}

.inputContainer:hover {
  border: 1px solid var(--ui-interactive-02-hover, #EBECEE);
}

/* Text input */
.numberInput {
  flex: 1 0 0;
  border: none;
  background: transparent;
  outline: none;
  
  color: var(--Text-text01, #FFF);
  font-feature-settings: 'liga' off, 'clig' off;
  
  /* Body-1 */
  font-family: "Amazon Ember";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 142.857% */
}

/* Remove default number input styling */
.numberInput::-webkit-outer-spin-button,
.numberInput::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.numberInput[type=number] {
  -moz-appearance: textfield;
}

/* Randomize button */
.randomizeButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  cursor: pointer;
  padding: 0;
  outline: none;
  transition: opacity 0.2s ease;
}

.randomizeButton:focus {
  outline: none;
}

.randomizeButton:hover {
  opacity: 1;
}

.randomizeButton svg {
  width: 24px;
  height: 24px;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.randomizeButton:hover svg {
  opacity: 1;
}
