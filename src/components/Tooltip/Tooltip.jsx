import React, { useState, useRef, useEffect } from 'react';
import Tag from '../Tag/Tag.jsx';
import styles from './Tooltip.module.css';

const Tooltip = ({ 
  children, 
  content, 
  label, 
  delay = 2000,
  position = 'auto' // 'auto', 'above', 'below', 'left', 'right'
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState({ position: 'above', alignment: 'middle' });
  const [showTooltip, setShowTooltip] = useState(false);
  const triggerRef = useRef(null);
  const tooltipRef = useRef(null);
  const timeoutRef = useRef(null);

  const calculatePosition = () => {
    if (!triggerRef.current) return;

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let newPosition = 'above';
    let alignment = 'middle';

    // Determine vertical position
    const spaceAbove = triggerRect.top;
    const spaceBelow = viewportHeight - triggerRect.bottom;
    const tooltipHeight = 200; // Approximate height

    if (position === 'auto') {
      if (spaceAbove >= tooltipHeight && spaceAbove > spaceBelow) {
        newPosition = 'above';
      } else if (spaceBelow >= tooltipHeight) {
        newPosition = 'below';
      } else {
        // Try left/right if not enough vertical space
        const spaceLeft = triggerRect.left;
        const spaceRight = viewportWidth - triggerRect.right;
        
        if (spaceRight >= 300) {
          newPosition = 'right';
        } else if (spaceLeft >= 300) {
          newPosition = 'left';
        } else {
          newPosition = spaceBelow > spaceAbove ? 'below' : 'above';
        }
      }
    } else {
      newPosition = position;
    }

    // Determine alignment
    if (newPosition === 'above' || newPosition === 'below') {
      const triggerCenter = triggerRect.left + triggerRect.width / 2;
      const tooltipWidth = 300;
      
      if (triggerCenter - tooltipWidth / 2 < 8) {
        alignment = 'left';
      } else if (triggerCenter + tooltipWidth / 2 > viewportWidth - 8) {
        alignment = 'right';
      } else {
        alignment = 'middle';
      }
    } else {
      // For left/right positions
      const triggerCenter = triggerRect.top + triggerRect.height / 2;
      const tooltipHeight = 200;
      
      if (triggerCenter - tooltipHeight / 2 < 8) {
        alignment = 'top';
      } else if (triggerCenter + tooltipHeight / 2 > viewportHeight - 8) {
        alignment = 'bottom';
      } else {
        alignment = 'middle';
      }
    }

    setTooltipPosition({ position: newPosition, alignment });
  };

  const handleMouseEnter = () => {
    timeoutRef.current = setTimeout(() => {
      setIsVisible(true);
      setTimeout(() => {
        setShowTooltip(true);
        calculatePosition();
      }, 50);
    }, delay);
  };

  const handleMouseLeave = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsVisible(false);
    setShowTooltip(false);
  };

  useEffect(() => {
    if (showTooltip) {
      calculatePosition();
      window.addEventListener('scroll', calculatePosition);
      window.addEventListener('resize', calculatePosition);
    }

    return () => {
      window.removeEventListener('scroll', calculatePosition);
      window.removeEventListener('resize', calculatePosition);
    };
  }, [showTooltip]);

  const getTooltipClasses = () => {
    const { position: pos, alignment } = tooltipPosition;
    return `${styles.tooltip} ${styles[pos]} ${styles[alignment]}`;
  };

  const ArrowSVG = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="6" viewBox="0 0 12 6" fill="none">
      <path d="M0.5 0L4.52569 5.22795C5.31837 6.25735 6.68163 6.25735 7.47431 5.22795L11.5 0H0.5Z" fill="#4F96FF"/>
    </svg>
  );

  return (
    <div className={styles.tooltipWrapper}>
      <div
        ref={triggerRef}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className={styles.trigger}
      >
        {children}
      </div>
      
      {isVisible && (
        <div
          ref={tooltipRef}
          className={getTooltipClasses()}
          style={{ opacity: showTooltip ? 1 : 0 }}
        >
          <div className={styles.tooltipContent}>
            {label && (
              <Tag variant="info">{label}</Tag>
            )}
            <div className={styles.tooltipBody}>
              <div className={styles.typography}>
                {content}
              </div>
            </div>
          </div>
          
          <div className={styles.pointerContainer}>
            <div className={styles.rectangle}></div>
            <div className={styles.arrowContainer}>
              <div className={styles.arrow}>
                <ArrowSVG />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Tooltip;
