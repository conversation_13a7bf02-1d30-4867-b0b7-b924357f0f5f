@import '../../styles/LUMINOUS_global_styles.css';

.tooltipWrapper {
  position: relative;
  display: inline-block;
}

.trigger {
  display: inline-block;
}

.tooltip {
  position: absolute;
  display: flex;
  width: 300px;
  min-width: 100px;
  max-width: 300px;
  flex-direction: column;
  align-items: flex-start;
  z-index: 1000;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

/* Tooltip Body container */
.tooltipContent {
  display: flex;
  padding: 8px;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  align-self: stretch;
  border-radius: 4px 4px 0px 0px;
  background: var(--ui-02, #1F252B);
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.75);
}

.tooltipBody {
  display: flex;
  align-items: center;
  gap: 10px;
  align-self: stretch;
}

.typography {
  flex: 1 0 0;
  color: var(--text-02, #C8CDD5);
  font-feature-settings: 'liga' off, 'clig' off;
  font-family: "Amazon Ember";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 142.857% */
  text-align: left;
}

/* Directional pointer container */
.pointerContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  align-self: stretch;
  background: transparent;
}

.rectangle {
  height: 4px;
  align-self: stretch;
  background: var(--status-informational, #4F96FF);
  border-radius: 0px 0px 4px 4px;
}

.arrowContainer {
  display: flex;
  padding: 0px 8px;
  flex-direction: column;
  align-items: center;
  align-self: stretch;
  position: relative;
  top: -1px;
}

.arrow {
  width: 11px;
  height: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Position: Above */
.tooltip.above {
  bottom: calc(100% + 10px);
}

.tooltip.above.left {
  left: 0;
}

.tooltip.above.middle {
  left: 50%;
  transform: translateX(-50%);
}

.tooltip.above.right {
  right: 0;
}

.tooltip.above .arrowContainer {
  transform: rotate(0deg);
}

/* Position: Below */
.tooltip.below {
  top: calc(100% + 10px);
}

.tooltip.below.left {
  left: 0;
}

.tooltip.below.middle {
  left: 50%;
  transform: translateX(-50%);
}

.tooltip.below.right {
  right: 0;
}

.tooltip.below .tooltipContent {
  border-radius: 0px 0px 4px 4px;
  order: 2;
}

.tooltip.below .pointerContainer {
  order: 1;
  flex-direction: column-reverse;
}

.tooltip.below .rectangle {
  border-radius: 4px 4px 0px 0px;
}

.tooltip.below .arrowContainer {
  transform: rotate(180deg);
  top: 1px;
}

/* Position: Left */
.tooltip.left {
  right: 100%;
  margin-right: 10px;
  top: 50%;
  transform: translateY(-50%);
  flex-direction: row;
}

.tooltip.left .tooltipContent {
  border-radius: 4px 0px 0px 4px;
  order: 1;
}

.tooltip.left .pointerContainer {
  flex-direction: row;
  order: 2;
  align-items: center;
  position: absolute;
  right: -12px;
  top: 0;
  bottom: 0;
  width: 16px;
}

.tooltip.left .rectangle {
  width: 4px;
  height: 100%;
  border-radius: 0px 4px 4px 0px;
  position: absolute;
  right: 12px;
  top: 0;
  bottom: 0;
}

.tooltip.left .arrowContainer {
  transform: rotate(-90deg);
  padding: 0px;
  position: absolute;
  right: 4px;
  top: 50%;
  transform-origin: center;
  transform: translateY(-50%) rotate(-90deg);
}

/* Position: Right */
.tooltip.right {
  left: 100%;
  margin-left: 10px;
  top: 50%;
  transform: translateY(-50%);
  flex-direction: row;
}

.tooltip.right .tooltipContent {
  border-radius: 0px 4px 4px 0px;
  order: 2;
}

.tooltip.right .pointerContainer {
  flex-direction: row-reverse;
  order: 1;
  align-items: center;
  position: absolute;
  left: -12px;
  top: 0;
  bottom: 0;
  width: 16px;
}

.tooltip.right .rectangle {
  width: 4px;
  height: 100%;
  border-radius: 4px 0px 0px 4px;
  position: absolute;
  left: 12px;
  top: 0;
  bottom: 0;
}

.tooltip.right .arrowContainer {
  transform: rotate(90deg);
  padding: 0px;
  position: absolute;
  left: 4px;
  top: 50%;
  transform-origin: center;
  transform: translateY(-50%) rotate(90deg);
}
