import React, { useState } from 'react';

const BaseTabs = ({ tabs = [], tabButtonClass, activeTabClass }) => {
  const [activeIndex, setActiveIndex] = useState(0);

  if (!tabs.length) return null;

  return (
    <div className="tabsWrapper">
      <div className="tabs">
        {tabs.map((tab, index) => (
          <button
            key={index}
            className={`${tabButtonClass} ${index === activeIndex ? activeTabClass : ''}`}
            onClick={() => setActiveIndex(index)}
          >
            {tab.label}
          </button>
        ))}
      </div>
      <div className="tabContent">{tabs[activeIndex].content}</div>
    </div>
  );
};

export default BaseTabs;