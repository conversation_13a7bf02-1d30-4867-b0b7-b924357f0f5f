@import '../../styles/LUMINOUS_global_styles.css';

.primaryTabContainer {
  display: flex;
  gap: 16px;
  padding: 16px;
  background-color: var(--ui-03);
}

.primaryTabButton {
  display: inline-flex;
  padding-bottom: 18px;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  border: none;
  background: transparent;
  color: var(--text-interactive-02, #C8CDD5);
  cursor: pointer;
  font-feature-settings: 'liga' off, 'clig' off;
  font-family: "Amazon Ember";
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 24px;
  transition: all 0.2s ease;
}

.primaryTabButton:hover {
  color: var(--text-interactive-02-hover, #EBECEE);
  border-bottom: none;
}

.primaryTabButton:focus {
  outline: none;
  border-bottom: none;
}

.activePrimaryTab {
  display: inline-flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  padding-bottom: 18px;
  border: none;
  border-bottom: 2px solid var(--ui-interactive-01, #78AEFF);
  color: var(--text-interactive-01, #78AEFF);
  font-feature-settings: 'liga' off, 'clig' off;
  font-family: "Amazon Ember";
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 24px;
}

.activePrimaryTab:hover {
  color: var(--text-interactive-01, #78AEFF);
  border-bottom: 2px solid var(--ui-interactive-01, #78AEFF);
}

/* ---------- Tertiary Tabs ---------- */
.tertiaryTabContainer {
  display: flex;
  gap: 10px;
  padding: 20px;
}

.tertiaryTabButton {
  display: inline-flex;
  padding: 8px 12px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 4px;
  border: none;
  background: none;
  color: var(--text-interactive-02, #C8CDD5);
  cursor: pointer;
  font-feature-settings: 'liga' off, 'clig' off;
  font-family: "Amazon Ember";
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
  line-height: 20px; /* 142.857% */
  transition: all 0.2s ease;
}

.tertiaryTabButton:hover {
  background: var(--ui-02-hover, #0D1114);
  color: var(--text-interactive-02-hover, #EBECEE);
}

.tertiaryTabButton:focus {
  outline: none;
}

.activeTertiaryTab {
  display: inline-flex;
  padding: 8px 12px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 24px;
  border-radius: 4px;
  background: var(--ui-03-hover, rgba(127, 133, 143, 0.20));
  color: var(--ui-interactive-01, #78AEFF);
}
