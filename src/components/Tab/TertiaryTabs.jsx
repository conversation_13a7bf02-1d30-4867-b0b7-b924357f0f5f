import React from 'react';
import styles from './Tab.module.css';

const TertiaryTabs = ({ tabs, onTabChange, activeTab: externalActiveTab }) => {
  const [internalActiveTab, setInternalActiveTab] = React.useState(0);

  // Use external activeTab if provided, otherwise use internal state
  const activeTab = externalActiveTab !== undefined ? externalActiveTab : internalActiveTab;

  // Handle both array of strings and array of objects
  const normalizedTabs = tabs.map((tab, index) => {
    if (typeof tab === 'string') {
      return { id: index, label: tab };
    }
    return tab;
  });

  const handleTabClick = (index) => {
    if (externalActiveTab === undefined) {
      setInternalActiveTab(index);
    }
    if (onTabChange) {
      onTabChange(index);
    }
  };

  return (
    <div className={styles.tertiaryTabContainer}>
      {normalizedTabs.map((tab, index) => {
        const isActive = activeTab === index;
        return (
          <button
            key={tab.id || index}
            className={`${styles.tertiaryTabButton} ${isActive ? styles.activeTertiaryTab : ''}`}
            onClick={() => handleTabClick(index)}
          >
            {tab.label}
          </button>
        );
      })}
    </div>
  );
};

export default TertiaryTabs;