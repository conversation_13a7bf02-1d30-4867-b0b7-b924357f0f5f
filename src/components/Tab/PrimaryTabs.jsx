import React from 'react';
import PropTypes from 'prop-types';
import BaseTabs from './BaseTabs';
import styles from './Tab.module.css';

const PrimaryTabs = ({ tabs }) => (
  <BaseTabs
    tabs={tabs}
    tabButtonClass={styles.primaryTabButton}
    activeTabClass={styles.primaryActiveTab}
  />
);

PrimaryTabs.propTypes = {
  tabs: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      content: PropTypes.node.isRequired
    })
  ).isRequired
};

export { PrimaryTabs };
