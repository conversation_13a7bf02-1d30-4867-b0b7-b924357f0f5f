import React, { useState } from 'react';
import TextField from './TextField';

/**
 * Example usage of the TextField component demonstrating all states and features
 */
const TextFieldExample = () => {
  const [controlledValue, setControlledValue] = useState('');
  const [draggableValue, setDraggableValue] = useState('');

  return (
    <div style={{ padding: '20px', display: 'flex', flexDirection: 'column', gap: '30px' }}>
      <h1>TextField Component Examples</h1>
      
      {/* Basic Usage */}
      <section>
        <h2>Basic Usage</h2>
        <TextField
          label="Basic Text Field"
          placeholder="Enter some text..."
          onChange={(value) => console.log('Basic field:', value)}
        />
      </section>

      {/* Without Label */}
      <section>
        <h2>Without Label</h2>
        <TextField
          showLabel={false}
          placeholder="This field has no label..."
          onChange={(value) => console.log('No label field:', value)}
        />
      </section>

      {/* Controlled Component */}
      <section>
        <h2>Controlled Component</h2>
        <TextField
          label="Controlled Field"
          placeholder="Type here..."
          value={controlledValue}
          onChange={setControlledValue}
        />
        <p style={{ color: '#C8CDD5', fontSize: '12px', marginTop: '8px' }}>
          Current value: "{controlledValue}"
        </p>
      </section>

      {/* Draggable Resize */}
      <section>
        <h2>Draggable Resize</h2>
        <TextField
          label="Resizable Field"
          placeholder="You can resize this field by dragging the corner..."
          draggable={true}
          value={draggableValue}
          onChange={setDraggableValue}
        />
      </section>

      {/* Disabled States */}
      <section>
        <h2>Disabled States</h2>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
          <TextField
            label="Empty Disabled"
            placeholder="This field is disabled..."
            disabled={true}
          />
          <TextField
            label="Filled Disabled"
            value="This field has content but is disabled"
            disabled={true}
          />
        </div>
      </section>

      {/* Custom Styling */}
      <section>
        <h2>Custom Styling</h2>
        <TextField
          label="Custom Class"
          placeholder="This field has custom styling..."
          className="custom-text-field"
          onChange={(value) => console.log('Custom field:', value)}
        />
      </section>

      {/* Form Integration Example */}
      <section>
        <h2>Form Integration</h2>
        <form onSubmit={(e) => { e.preventDefault(); console.log('Form submitted'); }}>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
            <TextField
              label="Name"
              placeholder="Enter your name..."
              id="name-field"
              required
            />
            <TextField
              label="Description"
              placeholder="Enter a description..."
              id="description-field"
              draggable={true}
            />
            <TextField
              label="Comments"
              placeholder="Additional comments..."
              showLabel={true}
              id="comments-field"
            />
            <button 
              type="submit" 
              style={{ 
                padding: '10px 16px', 
                backgroundColor: '#78AEFF', 
                color: 'white', 
                border: 'none', 
                borderRadius: '4px',
                cursor: 'pointer',
                alignSelf: 'flex-start'
              }}
            >
              Submit
            </button>
          </div>
        </form>
      </section>
    </div>
  );
};

export default TextFieldExample;
