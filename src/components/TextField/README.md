# TextField Component

A versatile text field component with multiple states, optional labeling, and draggable resize functionality.

## Features

- **6 Visual States**: default, hover, focused/pressed/typing, filled, filled-disabled, and disabled
- **Optional Label**: Toggle label visibility with `showLabel` prop
- **Draggable Resize**: Enable/disable resize functionality with `draggable` prop (resize handle appears at bottom-right corner)
- **Overflow Menu**: Optional dropdown menu with pre-canned prompts for quick text population
- **Controlled/Uncontrolled**: Supports both controlled and uncontrolled usage patterns
- **Accessibility**: Full keyboard navigation and screen reader support
- **Design System**: Follows LUMINOUS design specifications with proper typography and colors

## States

### Default
- Gray border (`#383E46`)
- Dark background (`#2A3038`)
- Italic placeholder text in gray (`#C8CDD5`)

### Hover
- Light gray border (`#C8CDD5`)
- Same background and text styling as default

### Focused/Pressed/Typing
- Blue border (`#78AEFF`)
- White text (`#FFF`)
- Normal font weight (not italic)

### Filled
- Gray border (`#383E46`)
- White text (`#FFF`)
- Normal font weight

### Filled-Disabled
- No border
- Darker background (`#1F252B`)
- Disabled text color (`#505760`)

### Disabled
- No border
- Darker background (`#1F252B`)
- Disabled text color (`#505760`)
- Italic placeholder text

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `label` | `string` | `"Label"` | The label text displayed above the text field |
| `showLabel` | `boolean` | `true` | Whether to show the label above the text field |
| `placeholder` | `string` | `"Enter text..."` | Placeholder text shown when field is empty |
| `value` | `string` | `""` | Controlled value of the text field |
| `onChange` | `function` | `undefined` | Callback function called when the value changes |
| `disabled` | `boolean` | `false` | Whether the text field is disabled |
| `draggable` | `boolean` | `false` | Whether the text field can be resized by dragging the resize handle |
| `showOverflowMenu` | `boolean` | `false` | Whether to show the overflow menu button |
| `overflowMenuItems` | `array` | `[]` | Array of menu items for the overflow menu |
| `multiSelect` | `boolean` | `false` | Whether overflow menu allows multiple selections |
| `showGroupLabels` | `boolean` | `false` | Whether to show group labels in overflow menu |
| `className` | `string` | `""` | Additional CSS classes to apply to the container |

## Usage

```jsx
import TextField from './components/TextField/TextField';

// Basic usage
<TextField />

// Without label
<TextField showLabel={false} />

// Controlled with custom props
<TextField
  label="Description"
  placeholder="Enter your description..."
  value={description}
  onChange={(value) => setDescription(value)}
/>

// Draggable resize enabled
<TextField
  label="Notes"
  draggable={true}
  placeholder="Add your notes here..."
/>

// Disabled state
<TextField
  label="Read-only Field"
  value="This cannot be edited"
  disabled={true}
/>

// With overflow menu (single select)
<TextField
  label="Prompt"
  placeholder="Select or type a prompt..."
  showOverflowMenu={true}
  overflowMenuItems={[
    { id: 'group1', type: 'group', label: 'Common Prompts' },
    { id: 'landscape', label: 'Beautiful landscape' },
    { id: 'portrait', label: 'Professional portrait' }
  ]}
  multiSelect={false}
  showGroupLabels={true}
/>

// With overflow menu (multi select)
<TextField
  label="Multiple Prompts"
  placeholder="Select multiple prompts..."
  showOverflowMenu={true}
  overflowMenuItems={menuItems}
  multiSelect={true}
  showGroupLabels={true}
/>
```

## Design Specifications

- **Container**: Inline-flex column layout, flex-start alignment
- **Label**: 12px Amazon Ember Medium, 8px bottom padding
- **Input Field**: 100px height, 10px 16px padding, 4px border radius
- **Text Area**: 368px width, 14px Amazon Ember font
- **Typography**: Amazon Ember font family with proper font weights and styles
- **Colors**: Uses CSS custom properties from the LUMINOUS design system

## Accessibility

- Semantic HTML with proper `textarea` element
- Label association with `htmlFor` attribute
- Keyboard navigation support (tab, enter, escape)
- Screen reader friendly with descriptive labels
- Focus management with visual focus indicators
- Disabled state properly communicated to assistive technologies

## Styling

The component uses CSS modules and follows the design system:
- Imports global styles from `LUMINOUS_global_styles.css`
- Uses CSS custom properties for colors and spacing
- Includes smooth transitions between states
- Responsive design with proper state management
