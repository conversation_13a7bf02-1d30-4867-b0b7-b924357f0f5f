@import '../../styles/LUMINOUS_global_styles.css';

/* Text field container (all states) */
.textFieldContainer {
  display: inline-flex;
  flex-direction: column;
  align-items: flex-start;
  width: 400px;
}

/* Label container */
.labelContainer {
  display: flex;
  width: 400px;
  padding: 0px 337px 8px 0px;
  align-items: center;
}

/* Label typography */
.label {
  color: var(--Text-text02, #C8CDD5);
  font-feature-settings: 'liga' off, 'clig' off;
  
  /* Label-1 */
  font-family: "Amazon Ember";
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 16px; /* 133.333% */
}

/* Input field base styles */
.inputField {
  display: flex;
  height: 100px;
  padding: 10px 10px 10px 16px;
  align-items: flex-start;
  gap: 10px;
  align-self: stretch;
  position: relative;

  border-radius: 4px;
  transition: border-color 0.2s ease, background-color 0.2s ease;
  box-sizing: border-box;
  overflow: hidden;
}

/* Textarea base styles */
.textarea {
  width: 100%;
  height: 100%;
  border: none;
  background: transparent;
  outline: none;
  font-feature-settings: 'liga' off, 'clig' off;
  font-family: "Amazon Ember";
  font-size: 14px;
  font-style: normal;
  line-height: 20px; /* 142.857% */
  resize: none;
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

/* Remove right padding when scrollbar is present */
.textarea::-webkit-scrollbar {
  width: 8px;
}

.textarea::-webkit-scrollbar-track {
  background: transparent;
}

.textarea::-webkit-scrollbar-thumb {
  background: var(--UI-uiLines01, #383E46);
  border-radius: 4px;
}

.textarea::-webkit-scrollbar-thumb:hover {
  background: var(--UI-uiInteractive02, #C8CDD5);
}

/* Default state */
.inputField.default {
  border: 1px solid var(--UI-uiLines01, #383E46);
  background: var(--UI-ui03, #2A3038);
}

.inputField.default .textarea {
  color: var(--Text-textInteractive02, #C8CDD5);
  font-weight: 300;
  font-style: italic;
}

.inputField.default .textarea::placeholder {
  color: var(--Text-textInteractive02, #C8CDD5);
  font-weight: 300;
  font-style: italic;
}

/* Hover state */
.inputField.hover {
  border: 1px solid var(--UI-uiInteractive02, #C8CDD5);
  background: var(--UI-ui03, #2A3038);
}

.inputField.hover .textarea {
  color: var(--Text-textInteractive02, #C8CDD5);
  font-weight: 300;
  font-style: italic;
}

.inputField.hover .textarea::placeholder {
  color: var(--Text-textInteractive02, #C8CDD5);
  font-weight: 300;
  font-style: italic;
}

/* Focused/pressed/typing state */
.inputField.focused {
  border: 1px solid var(--UI-uiInteractive01, #78AEFF);
  background: var(--UI-ui03, #2A3038);
}

.inputField.focused .textarea {
  color: var(--Text-text01, #FFF);
  font-weight: 400;
  font-style: normal;
}

.inputField.focused .textarea::placeholder {
  color: var(--Text-textInteractive02, #C8CDD5);
  font-weight: 300;
  font-style: italic;
}

/* Filled state */
.inputField.filled {
  border: 1px solid var(--UI-uiLines01, #383E46);
  background: var(--UI-ui03, #2A3038);
}

.inputField.filled .textarea {
  color: var(--Text-text01, #FFF);
  font-weight: 400;
  font-style: normal;
}

/* Filled-disabled state */
.inputField.filled-disabled {
  border: none;
  background: var(--UI-ui02, #1F252B);
}

.inputField.filled-disabled .textarea {
  color: var(--Text-textInteractiveDisabled, #505760);
  font-weight: 400;
  font-style: normal;
}

.inputField.filled-disabled .label {
  color: var(--Text-textInteractiveDisabled, #505760);
}

/* Disabled state */
.inputField.disabled {
  border: none;
  background: var(--UI-ui02, #1F252B);
}

.inputField.disabled .textarea {
  color: var(--Text-textInteractiveDisabled, #505760);
  font-weight: 300;
  font-style: italic;
}

.inputField.disabled .textarea::placeholder {
  color: var(--Text-textInteractiveDisabled, #505760);
  font-weight: 300;
  font-style: italic;
}

.inputField.disabled .label {
  color: var(--Text-textInteractiveDisabled, #505760);
}

/* Draggable resize functionality */
.inputField.draggable {
  resize: both;
  width: 400px;
  min-width: 400px;
  min-height: 100px;
  overflow: hidden;
  align-self: flex-start;
}

.inputField.draggable .textarea {
  resize: none;
}

/* Ensure disabled state overrides hover */
.inputField.disabled:hover {
  border: none;
  background: var(--UI-ui02, #1F252B);
}

.inputField.filled-disabled:hover {
  border: none;
  background: var(--UI-ui02, #1F252B);
}

/* Overflow button styles */
.overflowButton {
  position: absolute;
  top: 8px;
  right: 0px;
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: none;
  border: none;
  cursor: pointer;
  outline: none;
  padding: 0;
  margin: 0;
}

.overflowButton:focus {
  outline: none;
}

.overflowButton svg {
  width: 24px;
  height: 24px;
  display: block;
}

.overflowButton svg path {
  fill: #C8CDD5;
}

.overflowButton:hover svg path {
  fill: #EBECEE;
}

.overflowButton:disabled svg path {
  fill: #505760;
}

/* Adjust textarea padding when overflow menu is present */
.inputField.withOverflow .textarea {
  padding-right: 24px;
}
