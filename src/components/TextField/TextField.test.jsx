import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import TextField from './TextField';

describe('TextField', () => {
  it('renders with default props', () => {
    render(<TextField />);
    
    expect(screen.getByRole('textbox')).toBeInTheDocument();
    expect(screen.getByText('Label')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter text...')).toBeInTheDocument();
  });

  it('renders without label when showLabel is false', () => {
    render(<TextField showLabel={false} />);
    
    expect(screen.queryByText('Label')).not.toBeInTheDocument();
    expect(screen.getByRole('textbox')).toBeInTheDocument();
  });

  it('renders with custom label and placeholder', () => {
    render(<TextField label="Custom Label" placeholder="Custom placeholder" />);
    
    expect(screen.getByText('Custom Label')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Custom placeholder')).toBeInTheDocument();
  });

  it('handles controlled value changes', () => {
    const handleChange = vi.fn();
    render(<TextField value="initial value" onChange={handleChange} />);
    
    const textarea = screen.getByRole('textbox');
    expect(textarea.value).toBe('initial value');
    
    fireEvent.change(textarea, { target: { value: 'new value' } });
    expect(handleChange).toHaveBeenCalledWith('new value');
  });

  it('handles uncontrolled input', () => {
    render(<TextField />);
    
    const textarea = screen.getByRole('textbox');
    fireEvent.change(textarea, { target: { value: 'typed text' } });
    
    expect(textarea.value).toBe('typed text');
  });

  it('applies disabled state correctly', () => {
    render(<TextField disabled />);
    
    const textarea = screen.getByRole('textbox');
    expect(textarea).toBeDisabled();
  });

  it('applies draggable resize when enabled', () => {
    render(<TextField draggable />);

    const inputField = screen.getByRole('textbox').parentElement;
    expect(inputField).toHaveClass('draggable');
  });

  it('does not apply resize when draggable is false', () => {
    render(<TextField draggable={false} />);

    const inputField = screen.getByRole('textbox').parentElement;
    expect(inputField).not.toHaveClass('draggable');
  });

  it('handles focus and blur events', async () => {
    render(<TextField />);
    
    const textarea = screen.getByRole('textbox');
    
    fireEvent.focus(textarea);
    await waitFor(() => {
      expect(textarea).toHaveFocus();
    });
    
    fireEvent.blur(textarea);
    await waitFor(() => {
      expect(textarea).not.toHaveFocus();
    });
  });

  it('handles mouse enter and leave events', () => {
    render(<TextField />);
    
    const inputField = screen.getByRole('textbox').parentElement;
    
    fireEvent.mouseEnter(inputField);
    fireEvent.mouseLeave(inputField);
    
    // Test passes if no errors are thrown
    expect(inputField).toBeInTheDocument();
  });

  it('does not trigger hover on disabled field', () => {
    render(<TextField disabled />);
    
    const inputField = screen.getByRole('textbox').parentElement;
    
    fireEvent.mouseEnter(inputField);
    
    // Test passes if no errors are thrown and disabled state is maintained
    expect(screen.getByRole('textbox')).toBeDisabled();
  });

  it('passes through additional props', () => {
    render(<TextField id="test-id" data-testid="custom-test" />);
    
    const textarea = screen.getByRole('textbox');
    expect(textarea).toHaveAttribute('id', 'test-id');
    expect(textarea).toHaveAttribute('data-testid', 'custom-test');
  });

  it('applies custom className', () => {
    render(<TextField className="custom-class" />);
    
    const container = screen.getByRole('textbox').closest('.textFieldContainer');
    expect(container).toHaveClass('custom-class');
  });
});
