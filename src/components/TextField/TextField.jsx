import { useState, useRef, useEffect } from 'react';
import SelectMenu from '../SelectMenu/SelectMenu';
import styles from './TextField.module.css';

const OverflowIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
    <path d="M12 9C12.825 9 13.5 8.325 13.5 7.5C13.5 6.675 12.825 6 12 6C11.175 6 10.5 6.675 10.5 7.5C10.5 8.325 11.175 9 12 9ZM12 10.5C11.175 10.5 10.5 11.175 10.5 12C10.5 12.825 11.175 13.5 12 13.5C12.825 13.5 13.5 12.825 13.5 12C13.5 11.175 12.825 10.5 12 10.5ZM12 15C11.175 15 10.5 15.675 10.5 16.5C10.5 17.325 11.175 18 12 18C12.825 18 13.5 17.325 13.5 16.5C13.5 15.675 12.825 15 12 15Z" fill="#C8CDD5"/>
  </svg>
);

const TextField = ({
  label = "Label",
  showLabel = true,
  placeholder = "Enter text...",
  value = "",
  onChange,
  disabled = false,
  draggable = false,
  showOverflowMenu = false,
  overflowMenuItems = [],
  multiSelect = false,
  showGroupLabels = false,
  className = "",
  ...props
}) => {
  const [internalValue, setInternalValue] = useState(value);
  const [isFocused, setIsFocused] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [selectedMenuItems, setSelectedMenuItems] = useState([]);
  const [menuPosition, setMenuPosition] = useState({ left: 0, top: 0 });
  const textareaRef = useRef(null);
  const overflowButtonRef = useRef(null);

  // Update internal value when prop changes
  useEffect(() => {
    setInternalValue(value);
  }, [value]);

  const handleChange = (e) => {
    const newValue = e.target.value;
    setInternalValue(newValue);
    if (onChange) {
      onChange(newValue);
    }
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const handleMouseEnter = () => {
    if (!disabled) {
      setIsHovered(true);
    }
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  const updateMenuPosition = () => {
    if (!overflowButtonRef.current) return;

    const buttonRect = overflowButtonRef.current.getBoundingClientRect();
    setMenuPosition({
      left: buttonRect.left,
      top: buttonRect.bottom + 4
    });
  };

  const handleOverflowClick = () => {
    if (!overflowButtonRef.current) return;

    updateMenuPosition();
    setIsMenuOpen(!isMenuOpen);
  };

  // Update menu position on scroll
  useEffect(() => {
    if (!isMenuOpen) return;

    const handleScroll = () => {
      updateMenuPosition();
    };

    window.addEventListener('scroll', handleScroll, true);
    window.addEventListener('resize', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll, true);
      window.removeEventListener('resize', handleScroll);
    };
  }, [isMenuOpen]);

  const handleMenuClose = () => {
    setIsMenuOpen(false);
  };

  const handleMenuSelectionChange = (selectedItems) => {
    setSelectedMenuItems(selectedItems);

    // Populate text field with selected items
    if (selectedItems.length > 0) {
      const selectedTexts = selectedItems.map(item => item.label);
      const newValue = multiSelect ? selectedTexts.join(', ') : selectedTexts[0];
      setInternalValue(newValue);
      if (onChange) {
        onChange(newValue);
      }
    } else {
      // Clear text field when no items are selected
      setInternalValue('');
      if (onChange) {
        onChange('');
      }
    }
  };

  // Determine the current state for styling
  const getState = () => {
    if (disabled && internalValue) return 'filled-disabled';
    if (disabled) return 'disabled';
    if (isFocused) return 'focused';
    if (internalValue && !isFocused) return 'filled';
    if (isHovered) return 'hover';
    return 'default';
  };

  const currentState = getState();

  // Build container classes
  const containerClasses = [
    styles.textFieldContainer,
    className
  ].filter(Boolean).join(' ');

  // Build input field classes
  const inputFieldClasses = [
    styles.inputField,
    styles[currentState],
    draggable ? styles.draggable : '',
    showOverflowMenu ? styles.withOverflow : ''
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses}>
      {showLabel && (
        <div className={styles.labelContainer}>
          <label className={styles.label} htmlFor={props.id}>
            {label}
          </label>
        </div>
      )}
      <div 
        className={inputFieldClasses}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <textarea
          ref={textareaRef}
          value={internalValue}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          disabled={disabled}
          className={styles.textarea}
          {...props}
        />
        {showOverflowMenu && (
          <button
            ref={overflowButtonRef}
            type="button"
            onClick={handleOverflowClick}
            className={styles.overflowButton}
            disabled={disabled}
          >
            <OverflowIcon />
          </button>
        )}
      </div>

      {showOverflowMenu && (
        <SelectMenu
          isOpen={isMenuOpen}
          onClose={handleMenuClose}
          items={overflowMenuItems}
          selectedItems={selectedMenuItems}
          onSelectionChange={handleMenuSelectionChange}
          multiSelect={multiSelect}
          showGroupLabels={showGroupLabels}
          position={menuPosition}
          triggerRef={overflowButtonRef}
        />
      )}
    </div>
  );
};

export default TextField;
