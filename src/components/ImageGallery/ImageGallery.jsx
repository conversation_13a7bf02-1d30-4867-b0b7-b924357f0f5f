import React, { useState } from 'react';
import TertiaryTabs from '../Tab/TertiaryTabs.jsx';
import GalleryItem from '../GalleryItem/GalleryItem.jsx';
import styles from './ImageGallery.module.css';

const ImageGallery = ({ items = [] }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [selectedItemId, setSelectedItemId] = useState(null);

  const tabData = ['Images', 'Videos', 'All'];

  // Filter items based on active tab
  const getFilteredItems = () => {
    switch (activeTab) {
      case 0: // Images
        return items.filter(item => item.type === 'image');
      case 1: // Videos
        return items.filter(item => item.type === 'video');
      case 2: // All
      default:
        return items;
    }
  };

  const handleItemClick = (itemId) => {
    setSelectedItemId(selectedItemId === itemId ? null : itemId);
  };

  const filteredItems = getFilteredItems();

  return (
    <div className={styles.imageGallery}>
      <div className={styles.tabsContainer}>
        <TertiaryTabs 
          tabs={tabData}
          onTabChange={setActiveTab}
          activeTab={activeTab}
        />
      </div>
      
      <div className={styles.galleryGrid}>
        {filteredItems.map((item) => (
          <GalleryItem
            key={item.id}
            imageUrl={item.imageUrl}
            prompt={item.prompt}
            isProcessing={item.isProcessing}
            isSelected={selectedItemId === item.id}
            onClick={() => handleItemClick(item.id)}
          />
        ))}
        
        {filteredItems.length === 0 && (
          <div className={styles.emptyState}>
            <p className={styles.emptyText}>
              No {tabData[activeTab].toLowerCase()} available
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ImageGallery;
