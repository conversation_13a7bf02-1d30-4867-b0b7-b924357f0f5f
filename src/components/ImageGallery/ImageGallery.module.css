@import '../../styles/LUMINOUS_global_styles.css';

.imageGallery {
  display: flex;
  width: 300px;
  padding: 12px 0px;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;
  border-radius: 4px;
  background: var(--ui-03, #2A3038);
}

.tabsContainer {
  display: flex;
  padding: 0px 12px;
  align-items: flex-start;
  gap: 20px;
  align-self: stretch;
}

.galleryGrid {
  display: flex;
  padding: 0px 12px;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  flex: 1 0 0;
}

.emptyState {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 120px;
}

.emptyText {
  color: var(--text-03, #6D737D);
  font-family: "Amazon Ember";
  font-size: 14px;
  font-style: italic;
  font-weight: 300;
  line-height: 20px;
  margin: 0;
  text-align: center;
}
