@import '../../styles/LUMINOUS_global_styles.css';

.dropdown {
  position: relative;
  display: inline-block;
  font-family: var(--font-primary);
  font-size: var(--body-1-size);
  color: var(--text-03);
}

.dropdownToggle {
  display: flex;
  padding: var(--Spacing-spacing02, 8px) var(--Spacing-spacing02, 8px) var(--Spacing-spacing02, 8px) var(--Spacing-spacing04, 16px);
  align-items: center;
  gap: var(--Spacing-spacing02, 8px);
  align-self: stretch;
  border-radius: var(--Radius-radius02, 4px);
  border: 1px solid var(--uiLines01, #383E46);
  background: var(--ui03, #2A3038);
  color: var(--text-03);
  cursor: default;
  transition: background-color 0.2s ease;
}

.dropdownToggle:hover {
  border: 1px solid var(--uiInteractive02Hover, #EBECEE);
  background: var(--ui03, #2A3038);
}

.dropdownToggle:focus {
  border: 1px solid var(--uiInteractive01, #78AEFF);
  background: var(--ui03, #2A3038);
  outline: none;
}

.dropdownMenu {
  display: block;
  position: absolute;
  background-color: var(--ui-03, #2A3038);
  min-width: 160px;
  box-shadow: 0 8px 16px rgba(0,0,0,0.2);
  border-radius: var(--Radius-radius02, 4px);
  border: 1px solid var(--uiLines01, #383E46);
  margin-top: 8px;
  z-index: 1;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.2s ease, visibility 0.2s ease;
}

.show {
  visibility: visible;
  opacity: 1;
}

.dropdownMenu:not(.show) {
  pointer-events: none;
}

.dropdownItem {
  color: var(--text-03);
  padding: 12px 16px;
  text-decoration: none;
  display: block;
  cursor: pointer;
  border-radius: var(--Radius-radius02, 4px);
  transition: background-color 0.2s ease, color 0.2s ease;
}

.dropdownItem:hover {
  background-color: var(--uiInteractive02Hover, #EBECEE);
  color: var(--text-03);
}

.dropdownItem:focus {
  outline: none;
  background-color: var(--uiInteractive01, #78AEFF);
  color: var(--text-01);
}

.dropdownItem:active {
  background-color: var(--uiInteractive01, #78AEFF);
  color: var(--text-01);
}

.dropdownToggle.filled {
  border: 1px solid var(--UI-uiLines01, #383E46);
  background: var(--UI-ui03, #2A3038);
}
