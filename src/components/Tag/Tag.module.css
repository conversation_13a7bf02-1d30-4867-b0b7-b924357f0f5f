@import '../../styles/LUMINOUS_global_styles.css';

/* Base tag styles */
.tag {
  display: inline-flex;
  padding: 2px 12px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 4px;
  text-align: center;
  font-feature-settings: 'liga' off, 'clig' off;
  font-family: "Amazon Ember";
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 16px; /* 133.333% */
}

/* Light variant */
.tag.light {
  background: var(--ui-interactive-02-hover, #EBECEE);
  color: var(--text-03, #0D1114);
}

/* Dark variant */
.tag.dark {
  background: var(--ui-overlay, rgba(13, 17, 20, 0.55));
  color: var(--text-01, #FFF);
}

/* Info variant */
.tag.info {
  background: var(--status-informational, #4F96FF);
  color: var(--text-03, #0D1114);
}

/* Error variant */
.tag.error {
  background: var(--status-error, #F74D52);
  color: var(--text-03, #0D1114);
}

/* Warning variant */
.tag.warning {
  background: var(--status-warning, #F7941F);
  color: var(--text-03, #0D1114);
}

/* Success variant */
.tag.success {
  background: var(--status-success, #8BD742);
  color: var(--text-03, #0D1114);
}