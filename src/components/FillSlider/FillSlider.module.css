@import '../../styles/LUMINOUS_global_styles.css';

.slider {
  position: relative;
  display: flex;
  width: 160px;
  height: 24px;
  padding: 0px 8px;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  border-radius: 4px;
  border: 1px solid var(--ui-lines-01, #383E46);
  background: var(--ui-03, #2A3038);
  cursor: pointer;
  user-select: none;
  transition: border-color 0.2s ease, color 0.2s ease;
  overflow: hidden;
}

.slider:hover,
.slider.active {
  border: 1px solid var(--ui-interactive-02-hover, #EBECEE);
}

.fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 24px;
  background: var(--ui-overlay, rgba(13, 17, 20, 0.55));
  border-radius: 4px 0 0 4px;
  transition: width 0.1s ease;
  pointer-events: none;
}

.label {
  position: relative;
  z-index: 1;
  color: var(--text-interactive-02, #C8CDD5);
  font-feature-settings: 'liga' off, 'clig' off;
  font-family: "Amazon Ember";
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 16px; /* 133.333% */
  transition: color 0.2s ease;
  pointer-events: none;
}

.slider:hover .label,
.slider.active .label {
  color: var(--text-interactive-02-hover, #EBECEE);
}

/* Handle border radius for different fill percentages */

/* When fill is at 100%, make it fully rounded */
.slider .fill[style*="width: 100%"] {
  border-radius: 4px;
}
