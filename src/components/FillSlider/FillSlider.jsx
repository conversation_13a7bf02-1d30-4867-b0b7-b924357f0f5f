import React, { useState, useRef } from 'react';
import styles from './FillSlider.module.css';

const FillSlider = ({ 
  initialValue = 0, 
  min = 0, 
  max = 100, 
  label = "Slider label",
  onChange 
}) => {
  const [value, setValue] = useState(initialValue);
  const [isDragging, setIsDragging] = useState(false);
  const sliderRef = useRef(null);

  const handleMouseDown = (e) => {
    setIsDragging(true);
    updateValue(e);
  };

  const handleMouseMove = (e) => {
    if (isDragging) {
      updateValue(e);
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const updateValue = (e) => {
    if (!sliderRef.current) return;

    const rect = sliderRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
    const newValue = Math.round((percentage / 100) * (max - min) + min);
    
    setValue(newValue);
    if (onChange) {
      onChange(newValue);
    }
  };

  // Calculate fill percentage
  const fillPercentage = ((value - min) / (max - min)) * 100;

  // Add event listeners when dragging
  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging]);

  return (
    <div 
      ref={sliderRef}
      className={`${styles.slider} ${isDragging ? styles.active : ''}`}
      onMouseDown={handleMouseDown}
    >
      <div 
        className={styles.fill}
        style={{ width: `${fillPercentage}%` }}
      />
      <span className={styles.label}>
        {label}: {value}%
      </span>
    </div>
  );
};

export default FillSlider;
