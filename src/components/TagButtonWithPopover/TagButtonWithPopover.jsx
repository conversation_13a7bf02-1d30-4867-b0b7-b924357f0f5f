import React, { useState, useRef } from 'react';
import Button from '../Button/Button.jsx';
import AspectRatioPopover from '../AspectRatioPopover/AspectRatioPopover.jsx';
import styles from './TagButtonWithPopover.module.css';

const TagButtonWithPopover = ({ initialRatio = '16:9' }) => {
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [selectedRatio, setSelectedRatio] = useState({ id: initialRatio, label: initialRatio });
  const buttonRef = useRef(null);

  const handleButtonClick = () => {
    setIsPopoverOpen(!isPopoverOpen);
  };

  const handlePopoverClose = () => {
    setIsPopoverOpen(false);
  };

  const handleRatioSelect = (ratio) => {
    setSelectedRatio(ratio);
  };



  return (
    <div className={styles.container}>
      <Button 
        ref={buttonRef}
        variant="tag" 
        onClick={handleButtonClick}
        className={styles.tagButton}
      >
        {selectedRatio.label}
      </Button>
      
      <AspectRatioPopover
        isOpen={isPopoverOpen}
        onClose={handlePopoverClose}
        onSelect={handleRatioSelect}
        selectedRatio={selectedRatio}
        buttonRef={buttonRef}
      />
    </div>
  );
};

export default TagButtonWithPopover;
