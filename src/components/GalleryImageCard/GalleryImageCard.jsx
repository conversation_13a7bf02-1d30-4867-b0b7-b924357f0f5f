import { useState, useRef, useEffect } from 'react';
import Checkbox from '../Checkbox/Checkbox.jsx';
import Tag from '../Tag/Tag.jsx';
import styles from './GalleryImageCard.module.css';

// SVG Icon Components
const ThumbsUpOutline = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
    <path fillRule="evenodd" clipRule="evenodd" d="M15.5 5.89133C15.5 3.99197 13.1008 3.16218 11.9274 4.65568L8.51397 9H4C3.44772 9 3 9.44772 3 10V19C3 19.5523 3.44772 20 4 20L17.2721 20C18.1399 20 18.9086 19.4404 19.1753 18.6146L21.5295 11.3243C21.9465 10.0327 20.9835 8.70969 19.6262 8.70969H15.5V5.89133ZM8 18V11H5V18H8ZM10 18L17.2721 18L19.6262 10.7097H14.5C13.9477 10.7097 13.5 10.262 13.5 9.70969L13.5 5.89132L10 10.3459V18Z" fill="currentColor"/>
  </svg>
);

const ThumbsUpFilled = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
    <path fillRule="evenodd" clipRule="evenodd" d="M11.9274 4.65568C13.1008 3.16218 15.5 3.99197 15.5 5.89133V8.70969H19.6262C20.9835 8.70969 21.9465 10.0327 21.5295 11.3243L19.1753 18.6146C18.9086 19.4404 18.1399 20 17.2721 20H10V7.10869L11.9274 4.65568Z" fill="currentColor"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M8 9H4C3.44772 9 3 9.44772 3 10V19C3 19.5523 3.44772 20 4 20H8V9Z" fill="currentColor"/>
  </svg>
);

const ThumbsDownOutline = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
    <path fillRule="evenodd" clipRule="evenodd" d="M15.5 17.9961C15.5 19.8954 13.1008 20.7252 11.9274 19.2317L8.51397 14.8874H4C3.44772 14.8874 3 14.4397 3 13.8874V4.88739C3 4.3351 3.44772 3.88739 4 3.88739L17.2721 3.88738C18.1399 3.88738 18.9086 4.44699 19.1753 5.2728L21.5295 12.5631C21.9465 13.8547 20.9835 15.1777 19.6262 15.1777H15.5V17.9961ZM8 5.88739V12.8874H5V5.88739H8ZM10 5.88738L17.2721 5.88738L19.6262 13.1777H14.5C13.9477 13.1777 13.5 13.6254 13.5 14.1777L13.5 17.9961L10 13.5415V5.88738Z" fill="currentColor"/>
  </svg>
);

const ThumbsDownFilled = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
    <path fillRule="evenodd" clipRule="evenodd" d="M11.9274 19.2317C13.1008 20.7252 15.5 19.8954 15.5 17.9961V15.1777H19.6262C20.9835 15.1777 21.9465 13.8547 21.5295 12.5631L19.1753 5.2728C18.9086 4.44699 18.1399 3.88738 17.2721 3.88738H10V16.7787L11.9274 19.2317Z" fill="currentColor"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M8 14.8874H4C3.44772 14.8874 3 14.4397 3 13.8874V4.88739C3 4.3351 3.44772 3.88739 4 3.88739H8V14.8874Z" fill="currentColor"/>
  </svg>
);

const RestyledImage = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
    <path d="M18 2L19.2728 4.72721L22 6L19.2728 7.27279L18 10L16.7272 7.27279L14 6L16.7272 4.72721L18 2Z" fill="currentColor"/>
    <path d="M13 3H5C4.45 3 3.97917 3.19583 3.5875 3.5875C3.19583 3.97917 3 4.45 3 5H13V3Z" fill="currentColor"/>
    <path d="M19 10H21V19C21 19.55 20.8042 20.0208 20.4125 20.4125C20.0208 20.8042 19.55 21 19 21H5C4.45 21 3.97917 20.8042 3.5875 20.4125C3.19583 20.0208 3 19.55 3 19V13H5V19H19V10Z" fill="currentColor"/>
    <path d="M18 17H6L9 13L11.25 16L14.25 12L18 17Z" fill="currentColor"/>
    <path d="M5.95459 8.04541L5 6L4.04541 8.04541L2 9L4.04541 9.9546L5 12L5.95459 9.9546L8 9L5.95459 8.04541Z" fill="currentColor"/>
  </svg>
);

const VideoAI = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
    <path d="M6 4L6.6364 5.3636L8 6L6.6364 6.6364L6 8L5.3636 6.6364L4 6L5.3636 5.3636L6 4Z" fill="currentColor"/>
    <path d="M8 16.885V9.11501C8 8.52251 8.6525 8.16251 9.155 8.48501L15.26 12.3625C15.725 12.6625 15.725 13.3375 15.26 13.63L9.155 17.515C8.6525 17.8375 8 17.4775 8 16.885Z" fill="currentColor"/>
    <path d="M17.6137 6.38631L16.5 4L15.3863 6.38631L13 7.5L15.3863 8.61369L16.5 11L17.6137 8.61369L20 7.5L17.6137 6.38631Z" fill="currentColor"/>
    <path d="M14 16L14.6364 17.3636L16 18L14.6364 18.6364L14 20L13.3636 18.6364L12 18L13.3636 17.3636L14 16Z" fill="currentColor"/>
  </svg>
);

const Download = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
    <path d="M15.4425 10.25H14.25V6.5C14.25 6.0875 13.9125 5.75 13.5 5.75H10.5C10.0875 5.75 9.75 6.0875 9.75 6.5V10.25H8.5575C7.89 10.25 7.5525 11.06 8.025 11.5325L11.4675 14.975C11.76 15.2675 12.2325 15.2675 12.525 14.975L15.9675 11.5325C16.44 11.06 16.11 10.25 15.4425 10.25ZM6.75 17.75C6.75 18.1625 7.0875 18.5 7.5 18.5H16.5C16.9125 18.5 17.25 18.1625 17.25 17.75C17.25 17.3375 16.9125 17 16.5 17H7.5C7.0875 17 6.75 17.3375 6.75 17.75Z" fill="currentColor"/>
  </svg>
);

const GalleryImageCard = ({
  type = 'image', // 'image' or 'video'
  imageUrl,
  videoUrl,
  fileName,
  fileSize,
  createdAt,
  duration, // For video cards (in seconds)
  isSelected = false,
  isChecked = false,
  feedback = null, // 'up', 'down', or null
  onSelect,
  onCheckboxChange,
  onFeedback,
  onRestyle,
  onVideoAI,
  onDownload,
  className = '',
  ...props
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [currentFeedback, setCurrentFeedback] = useState(feedback);
  const [currentTime, setCurrentTime] = useState(0);
  const videoRef = useRef(null);

  // Handle video autoplay on hover
  useEffect(() => {
    if (type === 'video' && videoRef.current) {
      if (isHovered) {
        videoRef.current.play().catch(console.error);
      } else {
        videoRef.current.pause();
        videoRef.current.currentTime = 0;
        setCurrentTime(0);
      }
    }
  }, [isHovered, type]);

  // Handle video time updates
  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    const options = {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      timeZoneName: 'short'
    };
    return date.toLocaleDateString('en-US', options);
  };

  // Format duration for video cards
  const formatDuration = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Get display duration (current time during playback, or total duration)
  const getDisplayDuration = () => {
    if (type === 'video' && isHovered && currentTime > 0) {
      return formatDuration(currentTime);
    }
    return formatDuration(duration || 0);
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  // Handle feedback clicks
  const handleFeedbackClick = (feedbackType) => {
    const newFeedback = currentFeedback === feedbackType ? null : feedbackType;
    setCurrentFeedback(newFeedback);
    if (onFeedback) {
      onFeedback(newFeedback);
    }
  };

  // Handle card click (for selection)
  const handleCardClick = (e) => {
    // Don't trigger selection if clicking on action buttons
    const actionButton = e.target.closest('button');

    if (actionButton) {
      return;
    }

    if (onSelect) {
      onSelect();
    }
  };

  // Handle checkbox change
  const handleCheckboxChange = (e) => {
    if (onCheckboxChange) {
      onCheckboxChange(e);
    }
  };

  // Determine container classes
  const getContainerClasses = () => {
    let classes = [styles.galleryImageCard];
    
    if (isSelected && isHovered) classes.push(styles.selectedHover);
    else if (isSelected) classes.push(styles.selected);
    else if (isHovered) classes.push(styles.hover);
    
    if (className) classes.push(className);
    
    return classes.join(' ');
  };



  return (
    <div
      className={getContainerClasses()}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={handleCardClick}
      {...props}
    >
      {/* Image Container */}
      <div className={styles.imageContainer}>
        <div className={styles.imageAndActionsContainer}>
          {/* Image/Video */}
          {type === 'video' && videoUrl ? (
            <video
              ref={videoRef}
              className={styles.image}
              src={videoUrl}
              poster={imageUrl}
              muted
              loop
              playsInline
              onTimeUpdate={handleTimeUpdate}
            />
          ) : (
            <div
              className={styles.image}
              style={{ backgroundImage: imageUrl ? `url(${imageUrl})` : 'none' }}
            />
          )}

          {/* Video Duration Tag */}
          {type === 'video' && duration && (
            <div className={styles.tagContainer}>
              <Tag variant="dark">{getDisplayDuration()}</Tag>
            </div>
          )}

          {/* Hover Overlay and Actions */}
          {isHovered && (
            <>
              <div className={styles.imageHoverOverlay} />
              <div className={styles.actionsContainer}>
                {/* Left Actions - Feedback */}
                <div className={styles.actionsLeft}>
                  <button
                    className={styles.actionButton}
                    onClick={() => handleFeedbackClick('up')}
                    aria-label="Thumbs up"
                  >
                    {currentFeedback === 'up' ? <ThumbsUpFilled /> : <ThumbsUpOutline />}
                  </button>
                  <button
                    className={styles.actionButton}
                    onClick={() => handleFeedbackClick('down')}
                    aria-label="Thumbs down"
                  >
                    {currentFeedback === 'down' ? <ThumbsDownFilled /> : <ThumbsDownOutline />}
                  </button>
                </div>

                {/* Right Actions - Contextual */}
                <div className={styles.actionsRight}>
                  <button
                    className={styles.actionButton}
                    onClick={onRestyle}
                    aria-label="Restyle image"
                  >
                    <RestyledImage />
                  </button>
                  <button
                    className={styles.actionButton}
                    onClick={onVideoAI}
                    aria-label="Video AI"
                  >
                    <VideoAI />
                  </button>
                  <button
                    className={styles.actionButton}
                    onClick={onDownload}
                    aria-label="Download"
                  >
                    <Download />
                  </button>
                </div>
              </div>
            </>
          )}

          {/* Checkbox for multi-select */}
          {(isHovered || isSelected) && (
            <div className={styles.checkboxContainer}>
              <Checkbox
                checked={isChecked}
                onChange={handleCheckboxChange}
                showLabel={false}
                className={styles.checkbox}
              />
            </div>
          )}
        </div>
      </div>

      {/* Metadata Container */}
      <div className={styles.metadataContainer}>
        {/* File Name Label */}
        <div className={styles.labelText}>
          {fileName}
        </div>

        {/* Size + Timestamp Container */}
        <div className={styles.sizeTimestampContainer}>
          <span className={styles.sizeText}>
            {formatFileSize(fileSize)}
          </span>
          
          {/* Ellipse Separator */}
          <svg className={styles.ellipse} xmlns="http://www.w3.org/2000/svg" width="2" height="2" viewBox="0 0 2 2" fill="none">
            <circle cx="1" cy="1" r="1" fill="#C8CDD5"/>
          </svg>
          
          <span className={styles.timestampText}>
            {formatTimestamp(createdAt)}
          </span>
        </div>
      </div>
    </div>
  );
};

export default GalleryImageCard;
