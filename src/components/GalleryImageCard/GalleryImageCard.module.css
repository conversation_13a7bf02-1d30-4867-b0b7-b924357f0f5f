@import '../../styles/LUMINOUS_global_styles.css';

/* Base Gallery Image Card Container */
.galleryImageCard {
  display: inline-flex;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--Spacing-spacing01, 4px);
  cursor: pointer;
  transition: all 0.2s ease;
}

/* Image Container */
.imageContainer {
  display: flex;
  padding: var(--Spacing-spacing02, 8px);
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: var(--Spacing-spacing02, 8px);
  border: 2px solid transparent;
  transition: all 0.2s ease;
  overflow: hidden;
}

/* Image and Actions Container */
.imageAndActionsContainer {
  width: 200px;
  height: 200px;
  position: relative;
  border-radius: var(--Spacing-spacing02, 8px);
  overflow: hidden;
}

/* Image/Video Element */
.image {
  width: 200px;
  height: 200px;
  flex-shrink: 0;
  aspect-ratio: 1/1;
  border-radius: var(--Spacing-spacing02, 8px);
  background: lightgray 50% / cover no-repeat;
  background-size: cover;
  background-position: center;
  object-fit: cover;
  object-position: center;
}

/* Video Duration Tag Container */
.tagContainer {
  display: flex;
  width: 200px;
  padding: var(--Spacing-spacing02, 8px);
  flex-direction: column;
  align-items: flex-start;
  gap: var(--Spacing-spacing00, 0px);
  position: absolute;
  top: 8px;
  left: 8px;
  pointer-events: none;
  z-index: 3;
}

/* Image Hover Overlay */
.imageHoverOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 200px;
  height: 200px;
  flex-shrink: 0;
  background: linear-gradient(180deg, rgba(13, 17, 20, 0.00) 0%, rgba(13, 17, 20, 0.00) 25%, rgba(13, 17, 20, 0.00) 75%, #0D1114 100%);
  pointer-events: none;
  z-index: 1;
}

/* Actions Container */
.actionsContainer {
  position: absolute;
  bottom: 0;
  left: 0;
  display: flex;
  width: 200px;
  justify-content: space-between;
  align-items: flex-end;
  z-index: 2;
}

/* Actions Left (Feedback) */
.actionsLeft {
  display: flex;
  padding: var(--Spacing-spacing02, 8px);
  align-items: center;
  gap: var(--Spacing-spacing02, 8px);
}

/* Actions Right (Contextual) */
.actionsRight {
  display: flex;
  padding: var(--Spacing-spacing02, 8px);
  align-items: center;
  gap: var(--Spacing-spacing02, 8px);
}

/* Action Button */
.actionButton {
  width: 24px;
  height: 24px;
  aspect-ratio: 1/1;
  background: none;
  border: none;
  outline: none;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.actionButton:focus {
  outline: none;
}

.actionButton svg {
  width: 24px;
  height: 24px;
  fill: var(--text-interactive-02, #C8CDD5);
  transition: fill 0.2s ease;
}

.actionButton:hover svg {
  fill: var(--text-interactive-02-hover, #EBECEE);
}

/* Checkbox Container */
.checkboxContainer {
  position: absolute;
  right: 14px;
  top: 8px;
  display: flex;
  height: 24px;
  align-items: center;
  gap: 8px;
}

/* Metadata Container */
.metadataContainer {
  display: flex;
  padding-left: var(--Spacing-spacing02, 8px);
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
}

/* Label Text */
.labelText {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  align-self: stretch;
  overflow: hidden;
  color: var(--Text-text01, #FFF);
  font-feature-settings: 'liga' off, 'clig' off;
  text-overflow: ellipsis;
  text-align: left;
  font-family: "Amazon Ember";
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 16px; /* 133.333% */
}

/* Size + Timestamp Container */
.sizeTimestampContainer {
  display: flex;
  align-items: center;
  gap: 9px;
  align-self: stretch;
}

/* Size Text */
.sizeText {
  color: var(--Text-text02, #C8CDD5);
  text-align: left;
  font-feature-settings: 'liga' off, 'clig' off;
  font-family: "Amazon Ember";
  font-size: 12px;
  font-style: italic;
  font-weight: 300;
  line-height: 16px; /* 133.333% */
  letter-spacing: 0.5px;
}

/* Ellipse Separator */
.ellipse {
  width: 2px;
  height: 2px;
}

.ellipse circle {
  fill: var(--Text-text02, #C8CDD5);
}

/* Timestamp Text */
.timestampText {
  color: var(--Text-text02, #C8CDD5);
  text-align: left;
  font-feature-settings: 'liga' off, 'clig' off;
  font-family: "Amazon Ember";
  font-size: 12px;
  font-style: italic;
  font-weight: 300;
  line-height: 16px; /* 133.333% */
  letter-spacing: 0.5px;
}

/* STATE: Default - no additional styles needed */
/* STATE: Hover - no additional container styles needed */

/* STATE: Selected */
.galleryImageCard.selected .imageContainer {
  border-color: var(--UI-uiInteractive01, #78AEFF);
}

/* STATE: Selected Hover */
.galleryImageCard.selectedHover .imageContainer {
  border-color: var(--UI-uiInteractive01Hover, #4F96FF);
}
