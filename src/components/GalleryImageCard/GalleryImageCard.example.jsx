import React, { useState } from 'react';
import GalleryImageCard from './GalleryImageCard';

const GalleryImageCardExample = () => {
  const [selectedCards, setSelectedCards] = useState(new Set());
  const [checkedCards, setCheckedCards] = useState(new Set());
  const [feedbacks, setFeedbacks] = useState({});

  // Sample data
  const sampleCards = [
    {
      id: 1,
      type: 'image',
      imageUrl: 'https://picsum.photos/200/200?random=1',
      fileName: 'sunset_landscape.png',
      fileSize: 2048576, // 2MB
      createdAt: new Date('2025-01-15T14:30:00Z'),
    },
    {
      id: 2,
      type: 'video',
      imageUrl: 'https://picsum.photos/200/200?random=2',
      videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_360x240_1mb.mp4',
      fileName: 'animation_sequence.mov',
      fileSize: 5242880, // 5MB
      createdAt: new Date('2025-01-15T16:45:00Z'),
      duration: 5, // 5 seconds
    },
    {
      id: 3,
      type: 'image',
      imageUrl: 'https://picsum.photos/200/200?random=3',
      fileName: 'abstract_art.png',
      fileSize: 1536000, // 1.5MB
      createdAt: new Date('2025-01-14T09:15:00Z'),
    },
    {
      id: 4,
      type: 'video',
      imageUrl: 'https://picsum.photos/200/200?random=4',
      videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_360x240_2mb.mp4',
      fileName: 'flowing_water.mov',
      fileSize: 8388608, // 8MB
      createdAt: new Date('2025-01-13T11:20:00Z'),
      duration: 17, // 17 seconds
    },
    {
      id: 5,
      type: 'image',
      imageUrl: 'https://picsum.photos/200/200?random=5',
      fileName: 'cityscape_night.png',
      fileSize: 3145728, // 3MB
      createdAt: new Date('2025-01-12T20:30:00Z'),
    },
    {
      id: 6,
      type: 'video',
      imageUrl: 'https://picsum.photos/200/200?random=6',
      videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_360x240_1mb.mp4',
      fileName: 'short_clip.mov',
      fileSize: 1048576, // 1MB
      createdAt: new Date('2025-01-11T13:45:00Z'),
      duration: 77, // 1:17
    }
  ];

  // Handle card selection
  const handleCardSelect = (cardId) => {
    const newSelected = new Set(selectedCards);
    if (newSelected.has(cardId)) {
      newSelected.delete(cardId);
      // Also uncheck when deselecting
      const newChecked = new Set(checkedCards);
      newChecked.delete(cardId);
      setCheckedCards(newChecked);
    } else {
      newSelected.add(cardId);
    }
    setSelectedCards(newSelected);
  };

  // Handle checkbox change
  const handleCheckboxChange = (cardId) => (e) => {
    const newChecked = new Set(checkedCards);
    if (e.target.checked) {
      newChecked.add(cardId);
      // Also select the card when checking
      const newSelected = new Set(selectedCards);
      newSelected.add(cardId);
      setSelectedCards(newSelected);
    } else {
      newChecked.delete(cardId);
    }
    setCheckedCards(newChecked);
  };

  // Handle feedback
  const handleFeedback = (cardId) => (feedback) => {
    setFeedbacks(prev => ({
      ...prev,
      [cardId]: feedback
    }));
    console.log(`Card ${cardId} feedback:`, feedback);
  };

  // Handle action buttons
  const handleRestyle = (cardId) => () => {
    console.log(`Restyle card ${cardId}`);
  };

  const handleVideoAI = (cardId) => () => {
    console.log(`Video AI for card ${cardId}`);
  };

  const handleDownload = (cardId) => () => {
    console.log(`Download card ${cardId}`);
  };

  return (
    <div style={{ padding: '20px' }}>
      <h2>Gallery Image Card Examples</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <h3>Interactive Gallery</h3>
        <p>Click cards to select, hover to see actions, use checkboxes for multi-select</p>
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fill, minmax(216px, 1fr))', 
          gap: '16px',
          maxWidth: '1200px'
        }}>
          {sampleCards.map((card) => (
            <GalleryImageCard
              key={card.id}
              type={card.type}
              imageUrl={card.imageUrl}
              videoUrl={card.videoUrl}
              fileName={card.fileName}
              fileSize={card.fileSize}
              createdAt={card.createdAt}
              duration={card.duration}
              isSelected={selectedCards.has(card.id)}
              isChecked={checkedCards.has(card.id)}
              feedback={feedbacks[card.id]}
              onSelect={() => handleCardSelect(card.id)}
              onCheckboxChange={handleCheckboxChange(card.id)}
              onFeedback={handleFeedback(card.id)}
              onRestyle={handleRestyle(card.id)}
              onVideoAI={handleVideoAI(card.id)}
              onDownload={handleDownload(card.id)}
            />
          ))}
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>State Information</h3>
        <div style={{ 
          background: 'var(--ui-03, #2A3038)', 
          padding: '16px', 
          borderRadius: '8px',
          color: 'var(--text-01, #FFF)',
          fontFamily: 'Amazon Ember'
        }}>
          <p><strong>Selected Cards:</strong> {Array.from(selectedCards).join(', ') || 'None'}</p>
          <p><strong>Checked Cards:</strong> {Array.from(checkedCards).join(', ') || 'None'}</p>
          <p><strong>Feedbacks:</strong> {Object.entries(feedbacks).map(([id, feedback]) => `${id}: ${feedback}`).join(', ') || 'None'}</p>
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>Individual State Examples</h3>
        <div style={{ 
          display: 'flex', 
          gap: '16px', 
          flexWrap: 'wrap',
          alignItems: 'flex-start'
        }}>
          <div>
            <h4>Default State</h4>
            <GalleryImageCard
              type="image"
              imageUrl="https://picsum.photos/200/200?random=10"
              fileName="default_state.png"
              fileSize={1024000}
              createdAt={new Date()}
            />
          </div>
          
          <div>
            <h4>Selected State</h4>
            <GalleryImageCard
              type="image"
              imageUrl="https://picsum.photos/200/200?random=11"
              fileName="selected_state.png"
              fileSize={2048000}
              createdAt={new Date()}
              isSelected={true}
              isChecked={true}
            />
          </div>
          
          <div>
            <h4>Video with Duration</h4>
            <GalleryImageCard
              type="video"
              imageUrl="https://picsum.photos/200/200?random=12"
              fileName="video_example.mov"
              fileSize={5120000}
              createdAt={new Date()}
              duration={125} // 2:05
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default GalleryImageCardExample;
