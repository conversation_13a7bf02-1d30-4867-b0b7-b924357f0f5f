# Gallery Image Card Component

A comprehensive gallery card component for displaying images and videos with contextual actions, metadata, and multi-select functionality. Designed for use in larger image gallery pages within the web app.

## Features

- **Two Card Types**: Image and Video cards with distinct behaviors
- **Four States**: default, hover, selected, and hover-selected with smooth transitions
- **Contextual Actions**: Thumbs up/down feedback, restyle, video AI, and download actions
- **Multi-Select**: Checkbox integration for batch operations
- **Metadata Display**: File name, size, and timestamp with proper formatting
- **Video Playback**: Auto-play on hover for video cards
- **Duration Tags**: Time duration display for video cards
- **Feedback System**: Toggle between thumbs up/down (mutually exclusive)

## Usage

### Basic Usage

```jsx
import GalleryImageCard from './components/GalleryImageCard/GalleryImageCard';

function MyGallery() {
  const [selectedCards, setSelectedCards] = useState(new Set());
  const [checkedCards, setCheckedCards] = useState(new Set());

  return (
    <GalleryImageCard
      type="image"
      imageUrl="https://example.com/image.jpg"
      fileName="beautiful_sunset.png"
      fileSize={2048576}
      createdAt={new Date()}
      isSelected={selectedCards.has(cardId)}
      isChecked={checkedCards.has(cardId)}
      onSelect={() => handleCardSelect(cardId)}
      onCheckboxChange={handleCheckboxChange(cardId)}
      onDownload={() => handleDownload(cardId)}
    />
  );
}
```

### Video Card

```jsx
<GalleryImageCard
  type="video"
  imageUrl="https://example.com/thumbnail.jpg"
  videoUrl="https://example.com/video.mp4"
  fileName="animation_sequence.mov"
  fileSize={5242880}
  createdAt={new Date()}
  duration={17} // 17 seconds
  onVideoAI={() => handleVideoAI(cardId)}
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `type` | `'image' \| 'video'` | `'image'` | Card type determines behavior and display |
| `imageUrl` | `string` | - | URL for the image/video thumbnail |
| `videoUrl` | `string` | - | URL for video source (video cards only) |
| `fileName` | `string` | - | Display name of the file |
| `fileSize` | `number` | - | File size in bytes |
| `createdAt` | `Date` | - | Creation timestamp |
| `duration` | `number` | - | Video duration in seconds (video cards only) |
| `isSelected` | `boolean` | `false` | Whether the card is selected |
| `isChecked` | `boolean` | `false` | Whether the checkbox is checked |
| `feedback` | `'up' \| 'down' \| null` | `null` | Current feedback state |
| `onSelect` | `function` | - | Callback when card is clicked for selection |
| `onCheckboxChange` | `function` | - | Callback when checkbox state changes |
| `onFeedback` | `function` | - | Callback when feedback buttons are clicked |
| `onRestyle` | `function` | - | Callback for restyle action |
| `onVideoAI` | `function` | - | Callback for video AI action |
| `onDownload` | `function` | - | Callback for download action |
| `className` | `string` | `''` | Additional CSS classes |

## States

### 1. Default State
- Basic card display with image/video and metadata
- No additional styling or interactions visible

### 2. Hover State
- Gradient overlay appears on image/video
- Contextual action buttons become visible
- Checkbox appears for multi-select
- Video auto-plays (for video cards)

### 3. Selected State
- Blue border around image container
- Checkbox remains visible and checked
- Maintains selected appearance

### 4. Selected Hover State
- Darker blue border (hover variant)
- All hover interactions remain active
- Checkbox shows selected-hover state

## Interactions

### Card Selection
- Click anywhere on the card (outside action areas) to select/deselect
- Selection toggles the blue border and checkbox visibility

### Multi-Select Checkbox
- Appears on hover or when card is selected
- Checking automatically selects the card
- Unchecking doesn't deselect the card (allows partial selection states)

### Feedback System
- Thumbs up/down buttons in top-left on hover
- Only one feedback type can be active at a time
- Clicking the same feedback type again removes it

### Contextual Actions
- Restyle, Video AI, and Download buttons in top-right on hover
- Each action triggers its respective callback

### Video Behavior
- Auto-plays on hover (muted, looped)
- Pauses and resets when hover ends
- Shows duration tag in bottom-left corner

## Styling

The component uses CSS modules with design tokens:

- **Colors**: Uses `--UI-uiInteractive01`, `--Text-text01`, etc.
- **Typography**: Amazon Ember font family with specific weights
- **Layout**: 200x200px image area with vertical metadata layout
- **Spacing**: Consistent padding and gaps using spacing variables
- **Transitions**: Smooth 0.2s ease transitions between states

## File Format Support

- **Images**: PNG files (as specified in requirements)
- **Videos**: MOV files (as specified in requirements)
- **Size Display**: Automatic formatting (B, KB, MB, GB)
- **Timestamp**: Local timezone with format "Mon D, YYYY H:MM TZ"

## Accessibility

- Proper ARIA labels for action buttons
- Keyboard navigation support through button elements
- Screen reader friendly with semantic HTML structure
- Focus management for interactive elements

## Integration

This component integrates with existing design system components:
- Uses `Checkbox` component for multi-select functionality
- Uses `Tag` component (dark variant) for video duration display
- Imports SVG icons from the assets folder
- Follows established CSS variable patterns
