@import '../../styles/LUMINOUS_global_styles.css';

/* Main container with vertical layout */
.container {
  display: inline-flex;
  height: 123px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 2px;
}

/* Upload Button Styles (when no image is uploaded) */
.uploadButton {
  display: flex;
  width: 60px;
  padding: var(--Spacing-spacing00, 0px);
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: var(--Spacing-spacing00, 0px);
  flex: 1 0 0;
  border-radius: var(--Radius-radius02, 4px);
  border: 1px solid var(--UI-uiLines01, #383E46);
  background: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.uploadButton.hovered {
  border: 1px solid var(--ui-interactive-01-hover, #4F96FF);
}

.uploadText {
  color: #FFF;
  font-feature-settings: 'liga' off, 'clig' off;
  font-family: "Amazon Ember";
  font-size: 10px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  margin-top: 2px;
  text-align: center;
}

.uploadButton.hovered .uploadText {
  color: var(--text-interactive-01-hover, #4F96FF);
}

/* Image Container Styles (when image is uploaded) */
.imageContainer {
  position: relative;
  width: 60px;
  flex: 1 0 0;
  border-radius: var(--Radius-radius02, 4px);
  border: 1px solid var(--UI-uiLines01, #383E46);
  overflow: visible;
  cursor: pointer;
  transition: all 0.2s ease;
}

.imageContainer.imageHovered {
  border-radius: var(--Radius-radius02, 4px);
  border: 1px solid var(--ui-interactive-02-hover, #EBECEE);
}

.image {
  width: 100%;
  height: 100%;
  flex-shrink: 0;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-color: lightgray;
  border-radius: var(--Radius-radius02, 4px);
  overflow: hidden;
}

/* Delete Button */
.deleteButton {
  position: absolute;
  top: -12px;
  right: -12px;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  background: none;
  border: none;
  cursor: pointer;
  z-index: 10;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.deleteButton:hover {
  transform: scale(1.1);
}

.deleteButton svg {
  filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.8));
}

/* Swap Button Styles */
.swapButton {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  border-radius: 4px;
}

.swapButton:focus {
  outline: none;
}

.swapButton:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.swapButton.swapHovered:not(:disabled) {
  background: var(--UI-ui03Hover, rgba(127, 133, 143, 0.20));
}
