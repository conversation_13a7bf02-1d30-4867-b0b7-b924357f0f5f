import React, { useState, useRef } from 'react';
import styles from './VideoFrameUpload.module.css';

const VideoFrameUpload = ({ 
  onFirstFrameChange, 
  onLastFrameChange, 
  initialFirstFrame = null,
  initialLastFrame = null 
}) => {
  const [firstFrame, setFirstFrame] = useState(initialFirstFrame);
  const [lastFrame, setLastFrame] = useState(initialLastFrame);
  const [firstFrameHovered, setFirstFrameHovered] = useState(false);
  const [lastFrameHovered, setLastFrameHovered] = useState(false);
  const [swapHovered, setSwapHovered] = useState(false);
  
  const firstFrameInputRef = useRef(null);
  const lastFrameInputRef = useRef(null);

  const handleFirstFrameSelect = (event) => {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const imageData = e.target.result;
        setFirstFrame(imageData);
        if (onFirstFrameChange) {
          onFirstFrameChange(file, imageData);
        }
      };
      reader.readAsDataURL(file);
    }
    // Reset the input value to allow selecting the same file again
    event.target.value = '';
  };

  const handleLastFrameSelect = (event) => {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const imageData = e.target.result;
        setLastFrame(imageData);
        if (onLastFrameChange) {
          onLastFrameChange(file, imageData);
        }
      };
      reader.readAsDataURL(file);
    }
    // Reset the input value to allow selecting the same file again
    event.target.value = '';
  };

  const handleUploadClick = (frameType) => {
    if (frameType === 'first') {
      firstFrameInputRef.current?.click();
    } else {
      lastFrameInputRef.current?.click();
    }
  };

  const handleDeleteImage = (e, frameType) => {
    e.stopPropagation();
    e.preventDefault();
    
    if (frameType === 'first') {
      setFirstFrame(null);
      if (onFirstFrameChange) {
        onFirstFrameChange(null, null);
      }
    } else {
      setLastFrame(null);
      if (onLastFrameChange) {
        onLastFrameChange(null, null);
      }
    }
  };

  const handleSwapFrames = () => {
    const tempFirst = firstFrame;
    const tempLast = lastFrame;
    
    setFirstFrame(tempLast);
    setLastFrame(tempFirst);
    
    if (onFirstFrameChange) {
      onFirstFrameChange(null, tempLast);
    }
    if (onLastFrameChange) {
      onLastFrameChange(null, tempFirst);
    }
  };

  const FirstFrameIcon = ({ isHovered }) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
      <path fillRule="evenodd" clipRule="evenodd" d="M13 4.75C14.1046 4.75 15 5.64543 15 6.75V18.75C15 19.8546 14.1046 20.75 13 20.75H11L10.7959 20.7393C9.78722 20.637 9 19.7857 9 18.75V6.75C9 5.64543 9.89543 4.75 11 4.75H13ZM11 5.75C10.4477 5.75 10 6.19772 10 6.75V18.75C10 19.3023 10.4477 19.75 11 19.75H13C13.5523 19.75 14 19.3023 14 18.75V6.75C14 6.19772 13.5523 5.75 13 5.75H11Z" fill={isHovered ? "#4F96FF" : "#C8CDD5"}/>
      <path fillRule="evenodd" clipRule="evenodd" d="M20 4.75C21.1046 4.75 22 5.64543 22 6.75V18.75C22 19.8546 21.1046 20.75 20 20.75H18L17.7959 20.7393C16.7872 20.637 16 19.7857 16 18.75V6.75C16 5.64543 16.8954 4.75 18 4.75H20ZM18 5.75C17.4477 5.75 17 6.19772 17 6.75V18.75C17 19.3023 17.4477 19.75 18 19.75H20C20.5523 19.75 21 19.3023 21 18.75V6.75C21 6.19772 20.5523 5.75 20 5.75H18Z" fill={isHovered ? "#4F96FF" : "#C8CDD5"}/>
      <path d="M2 6.75C2 5.64543 2.89543 4.75 4 4.75H6C7.10457 4.75 8 5.64543 8 6.75V18.75C8 19.8546 7.10457 20.75 6 20.75H4C2.89543 20.75 2 19.8546 2 18.75V6.75Z" fill={isHovered ? "#4F96FF" : "#C8CDD5"}/>
    </svg>
  );

  const LastFrameIcon = ({ isHovered }) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
      <path fillRule="evenodd" clipRule="evenodd" d="M11 4.75C9.89543 4.75 9 5.64543 9 6.75V18.75C9 19.8546 9.89543 20.75 11 20.75H13L13.2041 20.7393C14.2128 20.637 15 19.7857 15 18.75V6.75C15 5.64543 14.1046 4.75 13 4.75H11ZM13 5.75C13.5523 5.75 14 6.19772 14 6.75V18.75C14 19.3023 13.5523 19.75 13 19.75H11C10.4477 19.75 10 19.3023 10 18.75V6.75C10 6.19772 10.4477 5.75 11 5.75H13Z" fill={isHovered ? "#4F96FF" : "#C8CDD5"}/>
      <path fillRule="evenodd" clipRule="evenodd" d="M4 4.75C2.89543 4.75 2 5.64543 2 6.75V18.75C2 19.8546 2.89543 20.75 4 20.75H6L6.20414 20.7393C7.21281 20.637 8 19.7857 8 18.75V6.75C8 5.64543 7.10457 4.75 6 4.75H4ZM6 5.75C6.55228 5.75 7 6.19772 7 6.75V18.75C7 19.3023 6.55228 19.75 6 19.75H4C3.44772 19.75 3 19.3023 3 18.75V6.75C3 6.19772 3.44772 5.75 4 5.75H6Z" fill={isHovered ? "#4F96FF" : "#C8CDD5"}/>
      <path d="M22 6.75C22 5.64543 21.1046 4.75 20 4.75H18C16.8954 4.75 16 5.64543 16 6.75V18.75C16 19.8546 16.8954 20.75 18 20.75H20C21.1046 20.75 22 19.8546 22 18.75V6.75Z" fill={isHovered ? "#4F96FF" : "#C8CDD5"}/>
    </svg>
  );

  const SwapIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
      {swapHovered && (
        <rect y="0.5" width="24" height="24" rx="4" fill="#7F858F" fillOpacity="0.2"/>
      )}
      <path d="M14.6667 19.3591L14.6667 8.93292L12.6095 8.93291C12.0626 8.93291 11.7953 8.29838 12.1841 7.93411L15.5625 4.66745C15.8056 4.44418 16.1823 4.44418 16.4253 4.66745L19.8159 7.93411C20.2047 8.29838 19.9374 8.93291 19.3905 8.93291L17.3333 8.93291V19.3591C17.3333 20.8803 14.6667 20.8803 14.6667 19.3591Z" fill={swapHovered ? "#EBECEE" : "#C8CDD5"}/>
      <path d="M6.66667 5.64087L6.66667 16.0671L4.60948 16.0671C4.06263 16.0671 3.79527 16.7016 4.18415 17.0659L7.56252 20.3326C7.80556 20.5558 8.18229 20.5558 8.42533 20.3326L11.8159 17.0659C12.2047 16.7016 11.9374 16.0671 11.3905 16.0671L9.33333 16.0671L9.33333 5.64087C9.33333 4.11972 6.66667 4.11972 6.66667 5.64087Z" fill={swapHovered ? "#EBECEE" : "#C8CDD5"}/>
    </svg>
  );

  const CloseIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path d="M12 4.5C7.8525 4.5 4.5 7.8525 4.5 12C4.5 16.1475 7.8525 19.5 12 19.5C16.1475 19.5 19.5 16.1475 19.5 12C19.5 7.8525 16.1475 4.5 12 4.5ZM15.2175 14.1675C15.51 14.46 15.51 14.9325 15.2175 15.225C14.925 15.5175 14.4525 15.5175 14.16 15.225L12 13.0575L9.8325 15.225C9.54 15.5175 9.0675 15.5175 8.775 15.225C8.4825 14.9325 8.4825 14.46 8.775 14.1675L10.9425 12L8.7825 9.8325C8.49 9.54 8.49 9.0675 8.7825 8.775C9.075 8.4825 9.5475 8.4825 9.84 8.775L12 10.9425L14.1675 8.775C14.46 8.4825 14.9325 8.4825 15.225 8.775C15.5175 9.0675 15.5175 9.54 15.225 9.8325L13.0575 12L15.2175 14.1675Z" fill="#C8CDD5"/>
    </svg>
  );

  const renderFrameButton = (frameType, image, isHovered, setHovered) => {
    if (!image) {
      return (
        <button
          className={`${styles.uploadButton} ${isHovered ? styles.hovered : ''}`}
          onClick={() => handleUploadClick(frameType)}
          onMouseEnter={() => setHovered(true)}
          onMouseLeave={() => setHovered(false)}
        >
          {frameType === 'first' ? <FirstFrameIcon isHovered={isHovered} /> : <LastFrameIcon isHovered={isHovered} />}
          <span className={styles.uploadText}>
            {frameType === 'first' ? 'First frame' : 'Last frame'}
          </span>
        </button>
      );
    }

    return (
      <div
        className={`${styles.imageContainer} ${isHovered ? styles.imageHovered : ''}`}
        onMouseEnter={() => setHovered(true)}
        onMouseLeave={() => setHovered(false)}
        onClick={() => handleUploadClick(frameType)}
      >
        <div 
          className={styles.image}
          style={{ backgroundImage: `url(${image})` }}
        />
        
        {isHovered && (
          <button
            className={styles.deleteButton}
            onClick={(e) => handleDeleteImage(e, frameType)}
            onMouseDown={(e) => e.stopPropagation()}
            onMouseUp={(e) => e.stopPropagation()}
          >
            <CloseIcon />
          </button>
        )}
      </div>
    );
  };

  return (
    <div className={styles.container}>
      {/* First Frame */}
      {renderFrameButton('first', firstFrame, firstFrameHovered, setFirstFrameHovered)}
      
      {/* Hidden file inputs */}
      <input
        ref={firstFrameInputRef}
        type="file"
        accept="image/*"
        onChange={handleFirstFrameSelect}
        style={{ display: 'none' }}
      />
      
      {/* Swap Icon */}
      <button
        className={`${styles.swapButton} ${swapHovered ? styles.swapHovered : ''}`}
        onClick={handleSwapFrames}
        onMouseEnter={() => setSwapHovered(true)}
        onMouseLeave={() => setSwapHovered(false)}
        disabled={!firstFrame && !lastFrame}
      >
        <SwapIcon />
      </button>
      
      {/* Last Frame */}
      {renderFrameButton('last', lastFrame, lastFrameHovered, setLastFrameHovered)}
      
      <input
        ref={lastFrameInputRef}
        type="file"
        accept="image/*"
        onChange={handleLastFrameSelect}
        style={{ display: 'none' }}
      />
    </div>
  );
};

export default VideoFrameUpload;
