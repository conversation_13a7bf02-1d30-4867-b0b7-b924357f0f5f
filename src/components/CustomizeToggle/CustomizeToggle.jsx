import React, { useState } from 'react';
import styles from './CustomizeToggle.module.css';

const CustomizeToggle = ({ 
  isSelected = false, 
  onToggle,
  className = ''
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = () => {
    if (onToggle) {
      onToggle(!isSelected);
    }
  };

  const getButtonClasses = () => {
    let classes = styles.customizeButton;
    if (isSelected) {
      classes += ` ${styles.selected}`;
      if (isHovered) {
        classes += ` ${styles.selectedHover}`;
      }
    } else if (isHovered) {
      classes += ` ${styles.hover}`;
    }
    return classes;
  };

  const CustomizeIcon = () => {
    const iconFill = isSelected 
      ? (isHovered ? '#0D1114' : '#0D1114')
      : (isHovered ? '#EBECEE' : '#C8CDD5');

    const backgroundFill = isSelected
      ? (isHovered ? '#4F96FF' : '#78AEFF')
      : (isHovered ? 'rgba(127, 133, 143, 0.2)' : 'none');

    return (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
        {(isHovered || isSelected) && (
          <rect width="24" height="24" rx="4" fill={backgroundFill} />
        )}
        <path 
          d="M12 20C11.5091 20 11.1111 19.602 11.1111 19.1111V15.5556C11.1111 15.0646 11.5091 14.6667 12 14.6667C12.4909 14.6667 12.8889 15.0646 12.8889 15.5556V16.4444H19.1111C19.602 16.4444 20 16.8424 20 17.3333C20 17.8243 19.602 18.2222 19.1111 18.2222H12.8889V19.1111C12.8889 19.602 12.4909 20 12 20ZM4.88889 18.2222C4.39797 18.2222 4 17.8243 4 17.3333C4 16.8424 4.39797 16.4444 4.88889 16.4444H8.44444C8.93536 16.4444 9.33333 16.8424 9.33333 17.3333C9.33333 17.8243 8.93536 18.2222 8.44444 18.2222H4.88889ZM8.44444 14.6667C7.95352 14.6667 7.55556 14.2687 7.55556 13.7778V12.8889H4.88889C4.39797 12.8889 4 12.4909 4 12C4 11.5091 4.39797 11.1111 4.88889 11.1111H7.55556V10.2222C7.55556 9.7313 7.95352 9.33333 8.44444 9.33333C8.93536 9.33333 9.33333 9.7313 9.33333 10.2222V13.7778C9.33333 14.2687 8.93536 14.6667 8.44444 14.6667ZM12 12.8889C11.5091 12.8889 11.1111 12.4909 11.1111 12C11.1111 11.5091 11.5091 11.1111 12 11.1111H19.1111C19.602 11.1111 20 11.5091 20 12C20 12.4909 19.602 12.8889 19.1111 12.8889H12ZM15.5556 9.33333C15.0646 9.33333 14.6667 8.93536 14.6667 8.44444V4.88889C14.6667 4.39797 15.0646 4 15.5556 4C16.0465 4 16.4444 4.39797 16.4444 4.88889V5.77778H19.1111C19.602 5.77778 20 6.17575 20 6.66667C20 7.15759 19.602 7.55556 19.1111 7.55556H16.4444V8.44444C16.4444 8.93536 16.0465 9.33333 15.5556 9.33333ZM4.88889 7.55556C4.39797 7.55556 4 7.15759 4 6.66667C4 6.17575 4.39797 5.77778 4.88889 5.77778H12C12.4909 5.77778 12.8889 6.17575 12.8889 6.66667C12.8889 7.15759 12.4909 7.55556 12 7.55556H4.88889Z" 
          fill={iconFill}
        />
      </svg>
    );
  };

  return (
    <button
      className={`${getButtonClasses()} ${className}`}
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      aria-label={`${isSelected ? 'Disable' : 'Enable'} customize options`}
    >
      <CustomizeIcon />
    </button>
  );
};

export default CustomizeToggle;
