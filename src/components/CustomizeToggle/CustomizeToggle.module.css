@import '../../styles/LUMINOUS_global_styles.css';

.customizeButton {
  display: flex;
  width: 24px;
  height: 24px;
  align-items: center;
  justify-content: center;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0;
  border-radius: 4px;
}

.customizeButton:focus {
  outline: none;
}

/* Default/Off state - no additional styling needed, handled by SVG */

/* Hover state - handled by SVG background */

/* Selected/On state - handled by SVG background */

/* Selected/On-Hover state - handled by SVG background */
