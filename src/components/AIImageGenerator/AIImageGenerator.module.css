@import '../../styles/LUMINOUS_global_styles.css';

.container {
  display: flex;
  flex-direction: column;
  gap: var(--Spacing-spacing02, 8px);
  padding: var(--Spacing-spacing03, 16px);
  border-radius: 8px;
  background: var(--ui-02, #1F252B);
  border: 1px solid var(--ui-lines-01, #383E46);
  max-width: 800px;
  width: 100%;
}

.promptSection {
  width: 100%;
}

.promptInput {
  width: 100%;
}

.controlsRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--Spacing-spacing03, 16px);
  flex-wrap: wrap;
}

.aspectRatioSection {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.actionButtons {
  display: flex;
  gap: var(--Spacing-spacing02, 8px);
  align-items: center;
  flex-shrink: 0;
}

.actionButton {
  min-width: 60px;
}

.rightControls {
  display: flex;
  align-items: center;
  gap: var(--Spacing-spacing03, 16px);
  flex-shrink: 0;
}

.generateButton {
  min-width: 120px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .controlsRow {
    flex-direction: column;
    align-items: stretch;
    gap: var(--Spacing-spacing02, 8px);
  }
  
  .aspectRatioSection,
  .actionButtons,
  .rightControls {
    justify-content: center;
  }
  
  .rightControls {
    flex-direction: column;
    gap: var(--Spacing-spacing02, 8px);
  }
}

@media (max-width: 480px) {
  .container {
    padding: var(--Spacing-spacing02, 8px);
  }
  
  .actionButtons {
    width: 100%;
    justify-content: space-between;
  }
  
  .actionButton {
    flex: 1;
  }
  
  .generateButton {
    width: 100%;
  }
}
