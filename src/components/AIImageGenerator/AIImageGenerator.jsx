import React, { useState } from 'react';
import styles from './AIImageGenerator.module.css';
import TextField from '../TextField/TextField.jsx';
import Button from '../Button/Button.jsx';
import NumberInput from '../NumberInput/NumberInput.jsx';
import AspectRatioSelector from '../AspectRatioSelector/AspectRatioSelector.jsx';
import { MagicWandIcon } from '../../assets/IconLibrary.jsx';

const AIImageGenerator = ({
  onGenerate,
  onStyleClick,
  onCompClick,
  className = ''
}) => {
  const [prompt, setPrompt] = useState('');
  const [aspectRatio, setAspectRatio] = useState('1:1');
  const [seed, setSeed] = useState(420530);

  const handleGenerate = () => {
    if (onGenerate) {
      onGenerate({
        prompt,
        aspectRatio,
        seed
      });
    }
  };

  const handleStyleClick = () => {
    if (onStyleClick) {
      onStyleClick();
    }
  };

  const handleCompClick = () => {
    if (onCompClick) {
      onCompClick();
    }
  };

  return (
    <div className={`${styles.container} ${className}`}>
      {/* Main prompt input */}
      <div className={styles.promptSection}>
        <TextField
          placeholder="Describe an image to generate"
          value={prompt}
          onChange={setPrompt}
          showLabel={false}
          className={styles.promptInput}
        />
      </div>

      {/* Controls row */}
      <div className={styles.controlsRow}>
        {/* Left side - Aspect ratio */}
        <div className={styles.aspectRatioSection}>
          <AspectRatioSelector
            selectedRatio={aspectRatio}
            onRatioChange={setAspectRatio}
          />
        </div>

        {/* Center - Action buttons */}
        <div className={styles.actionButtons}>
          <Button
            variant="secondary"
            onClick={handleStyleClick}
            className={styles.actionButton}
          >
            Style
          </Button>
          <Button
            variant="secondary"
            onClick={handleCompClick}
            className={styles.actionButton}
          >
            Comp
          </Button>
        </div>

        {/* Right side - Seed and Generate */}
        <div className={styles.rightControls}>
          <NumberInput
            label="Seed"
            initialValue={seed}
            min={0}
            max={999999}
            onChange={setSeed}
          />
          <Button
            variant="amazon-primary"
            icon={<MagicWandIcon />}
            onClick={handleGenerate}
            className={styles.generateButton}
          >
            Generate
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AIImageGenerator;
