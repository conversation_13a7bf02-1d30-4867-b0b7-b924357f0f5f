import React, { forwardRef, cloneElement } from 'react';
import styles from './Button.module.css';

const Button = forwardRef(({ children, variant = 'primary', className, icon, iconPosition = 'left', ...props }, ref) => {
  const buttonClasses = `${styles.button} ${styles[variant] || ''} ${className || ''}`;

  // Special handling for new/add button variants
  if (variant === 'new' || variant === 'add') {
    const PlusIcon = () => (
      <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 36 36" fill="none" className={styles.plusIcon}>
        <path d="M24.75 19.125H19.125V24.75C19.125 25.3687 18.6187 25.875 18 25.875C17.3812 25.875 16.875 25.3687 16.875 24.75V19.125H11.25C10.6312 19.125 10.125 18.6187 10.125 18C10.125 17.3812 10.6312 16.875 11.25 16.875H16.875V11.25C16.875 10.6312 17.3812 10.125 18 10.125C18.6187 10.125 19.125 10.6312 19.125 11.25V16.875H24.75C25.3687 16.875 25.875 17.3812 25.875 18C25.875 18.6187 25.3687 19.125 24.75 19.125Z"/>
      </svg>
    );

    return (
      <button {...props} ref={ref} className={buttonClasses}>
        <div className={styles.iconButton}>
          <PlusIcon />
        </div>
        {children && <span className={styles.buttonLabel}>{children}</span>}
      </button>
    );
  }

  // Helper function to recursively remove fill attributes from SVG elements
  const removeFillAttributes = (element) => {
    if (!element || !element.props) return element;

    const newProps = { ...element.props };

    // Remove fill attribute if it exists
    if (newProps.fill) {
      delete newProps.fill;
    }

    // Recursively process children
    if (newProps.children) {
      if (Array.isArray(newProps.children)) {
        newProps.children = newProps.children.map(child =>
          React.isValidElement(child) ? removeFillAttributes(child) : child
        );
      } else if (React.isValidElement(newProps.children)) {
        newProps.children = removeFillAttributes(newProps.children);
      }
    }

    return cloneElement(element, newProps);
  };

  // Render icon with proper className for styling and remove hardcoded fills
  const renderIcon = () => {
    if (!icon) return null;

    // If icon is a React element, clone it and add the icon class
    if (React.isValidElement(icon)) {
      const cleanIcon = removeFillAttributes(icon);
      return cloneElement(cleanIcon, {
        className: `${styles.icon} ${cleanIcon.props.className || ''}`.trim()
      });
    }

    // If icon is a function component, render it and clean it
    if (typeof icon === 'function') {
      const IconComponent = icon;
      const iconElement = <IconComponent />;
      const cleanIcon = removeFillAttributes(iconElement);
      return cloneElement(cleanIcon, {
        className: `${styles.icon} ${cleanIcon.props.className || ''}`.trim()
      });
    }

    return null;
  };

  // Determine content order based on icon position
  const iconElement = renderIcon();
  const content = iconPosition === 'right'
    ? [children, iconElement].filter(Boolean)
    : [iconElement, children].filter(Boolean);

  return (
    <button {...props} ref={ref} className={buttonClasses}>
      {content.map((item, index) => (
        <React.Fragment key={index}>{item}</React.Fragment>
      ))}
    </button>
  );
});

Button.displayName = 'Button';

export default Button;