@import '../../styles/LUMINOUS_global_styles.css';

/* Ensure icons are properly spaced and visible */
.button {
  display: inline-flex;
  align-items: center;
  gap: 8px; /* Space between icon and text */
  height: 40px;
  padding: 10px 16px;
  flex-shrink: 0;

  border-radius: 4px;
  font-family: var(--font-primary);
  font-size: var(--body-1-size);
  font-weight: var(--body-1-weight);
  cursor: pointer;
  border: none;
  transition: background 0.2s ease, border 0.2s ease, color 0.2s ease;
}

/* Default Icon Style */
.button .icon {
  width: 24px;
  height: 24px;
  display: inline-block;
  transition: color 0.2s ease; /* Smooth color transitions */
  flex-shrink: 0; /* Prevent icon from shrinking */
  color: currentColor; /* Inherit button text color */
}

/* Force all SVG elements to inherit color from parent */
.button .icon,
.button .icon *,
.button .icon svg,
.button .icon svg * {
  fill: currentColor !important;
  color: currentColor !important;
}

/* Primary Button */
.button.primary {
  border-radius: 4px;
  background: var(--ui-interactive-01, #78AEFF);
  color: var(--text-03);
}

.button.primary:hover {
  border-radius: 4px;
  background: var(--ui-interactive-01-hover, #4F96FF);
}

/* Primary and Amazon Primary Button Icons */
.button.primary:not(:disabled) .icon,
.button.primary:not(:disabled) .icon *,
.button.amazon-primary:not(:disabled) .icon,
.button.amazon-primary:not(:disabled) .icon * {
  fill: var(--text-03) !important;
  color: var(--text-03) !important;
}

.button.primary:not(:disabled):hover .icon,
.button.primary:not(:disabled):hover .icon *,
.button.amazon-primary:not(:disabled):hover .icon,
.button.amazon-primary:not(:disabled):hover .icon * {
  fill: var(--text-03) !important;
  color: var(--text-03) !important;
}

/* Remove border on click (active state) */
.button.primary:active {
  background: var(--ui-interactive-01-hover, #4F96FF);
  border: none;
}

.button.primary:disabled {
  border-radius: 4px;
  background: var(--ui-interactive-disabled, #505760);
  color: var(--text-text-interactive-disabled);
  cursor: not-allowed;
  opacity: 0.5;
}

/* Ensure no default browser focus outline unless needed */
.button.primary:focus-visible {
  outline: 2px solid var(--ui-interactive-01-hover);
  outline-offset: 2px;
}

/* Amazon Primary Button */
.button.amazon-primary {
  border-radius: 4px;
  background: var(--ui-interactive-03, #FF9900);
  color: var(--text-03, #0D1114);
}

.button.amazon-primary:hover {
  background: var(--ui-interactive-03-hover, #F18302);
  color: var(--text-03, #0D1114);
}

.button.amazon-primary:disabled {
  background: var(--ui-interactive-disabled);
  color: var(--text-text-interactive-disabled);
  cursor: not-allowed;
  opacity: 0.5;
}

/* Secondary Button */
.button.secondary {
  border-radius: 4px;
  border: 1px solid var(--ui-interactive-01, #78AEFF);
  color: var(--text-interactive-01, #78AEFF);
  background: transparent;
}

.button.secondary:hover {
  border-radius: 4px;
  border: 1px solid var(--ui-interactive-01-hover, #4F96FF);
  color: var(--ui-interactive-01-hover, #4F96FF);
}

/* Secondary Button Icons - inherit from text color */
.button.secondary:not(:disabled) .icon,
.button.secondary:not(:disabled) .icon * {
  fill: var(--text-interactive-01, #78AEFF) !important;
  color: var(--text-interactive-01, #78AEFF) !important;
}

.button.secondary:not(:disabled):hover .icon,
.button.secondary:not(:disabled):hover .icon * {
  fill: var(--ui-interactive-01-hover, #4F96FF) !important;
  color: var(--ui-interactive-01-hover, #4F96FF) !important;
}

.button.secondary:disabled {
  border-radius: 4px;
  border: 1px solid var(--ui-interactive-disabled, #505760);
  color: var(--text-interactive-disabled, #505760);
  cursor: not-allowed;
  opacity: 0.5;
}

/* Tertiary Button */
.button.tertiary {
  background: transparent;
  color: var(--text-interactive-02, #C8CDD5);
  border: 1px solid var(--ui-interactive-02, #C8CDD5);
}

.button.tertiary:hover {
  background: transparent;
  color: var(--ui-interactive-02-hover, #EBECEE);
  border: 1px solid var(--ui-interactive-02-hover, #EBECEE);
}

/* Tertiary Button Icons - inherit from text/border color */
.button.tertiary:not(:disabled) .icon,
.button.tertiary:not(:disabled) .icon * {
  fill: var(--text-interactive-02, #C8CDD5) !important;
  color: var(--text-interactive-02, #C8CDD5) !important;
}

.button.tertiary:not(:disabled):hover .icon,
.button.tertiary:not(:disabled):hover .icon * {
  fill: var(--ui-interactive-02-hover, #EBECEE) !important;
  color: var(--ui-interactive-02-hover, #EBECEE) !important;
}

.button.tertiary:disabled {
  border-color: var(--ui-interactive-disabled);
  color: var(--text-interactive-disabled);
  cursor: not-allowed;
  opacity: 0.5;
}

/* Ghost Button */
.button.ghost {
  display: inline-flex;
  height: 40px;
  padding: 8px 0px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
  background: transparent;
  color: var(--text-interactive-01, #78AEFF);
}

.button.ghost:hover {
  background: transparent;
  color: var(--ui-interactive-01-hover, #4F96FF);
}

/* Ghost Button Icons - inherit from text color */
.button.ghost:not(:disabled) .icon,
.button.ghost:not(:disabled) .icon * {
  fill: var(--text-interactive-01, #78AEFF) !important;
  color: var(--text-interactive-01, #78AEFF) !important;
}

.button.ghost:not(:disabled):hover .icon,
.button.ghost:not(:disabled):hover .icon * {
  fill: var(--ui-interactive-01-hover, #4F96FF) !important;
  color: var(--ui-interactive-01-hover, #4F96FF) !important;
}

/* Tag Button */
.button.tag {
  display: inline-flex;
  padding: 2px 8px;
  align-items: center;
  gap: 4px;
  height: 24px; /* Fixed height to 24px */
  border-radius: 4px;
  border: 1px solid var(--ui-lines-02, #6D737D);
  background: transparent;
  color: var(--text-interactive-02, #C8CDD5);
  font-feature-settings: 'liga' off, 'clig' off;
  font-family: "Amazon Ember";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px; /* Reduced from 20px to 16px */
  box-sizing: border-box; /* Ensure border is included in height */
}

.button.tag:hover,
.button.tag:active {
  border: 1px solid var(--ui-interactive-02-hover, #EBECEE);
  background: var(--ui-overlay, rgba(13, 17, 20, 0.55));
  color: var(--text-interactive-02-hover, #EBECEE);
}

.button.tag .icon {
  width: 16px;
  height: 16px;
  fill: currentColor;
}

.button.tag:hover .icon,
.button.tag:active .icon {
  fill: var(--text-interactive-02-hover, #EBECEE);
}

/* New/Add Button */
.button.new,
.button.add {
  display: inline-flex;
  padding: 4px;
  flex-direction: column;
  align-items: center;
  gap: 0px;
  height: auto;
  border-radius: 8px;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
}

.button.new .iconButton,
.button.add .iconButton {
  display: flex;
  width: 72px;
  height: 72px;
  padding: 0px;
  justify-content: center;
  align-items: center;
  gap: 0px;
  aspect-ratio: 1/1;
  border-radius: 4px;
  border: 1px solid var(--ui-lines-01, #383E46);
  background: var(--ui-03, #2A3038);
  transition: all 0.2s ease;
}

.button.new:hover .iconButton,
.button.add:hover .iconButton {
  border: 1px solid var(--ui-interactive-01-hover, #4F96FF);
  background: var(--ui-03, #2A3038);
}

.button.new .plusIcon,
.button.add .plusIcon {
  width: 36px;
  height: 36px;
  flex-shrink: 0;
  fill: var(--ui-interactive-02, #C8CDD5);
  transition: fill 0.2s ease;
}

.button.new:hover .plusIcon,
.button.add:hover .plusIcon {
  fill: var(--ui-interactive-01-hover, #4F96FF);
}

.button.new .buttonLabel,
.button.add .buttonLabel {
  margin-top: 4px;
  color: var(--text-interactive-02, #C8CDD5);
  font-family: "Amazon Ember";
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
  text-align: center;
}

.button.new:hover .buttonLabel,
.button.add:hover .buttonLabel {
  color: var(--text-interactive-02-hover, #EBECEE);
}

/* Disabled Button Icons - inherit from disabled button text color with high specificity */
.button.primary:disabled .icon,
.button.primary:disabled .icon *,
.button.amazon-primary:disabled .icon,
.button.amazon-primary:disabled .icon *,
.button.secondary:disabled .icon,
.button.secondary:disabled .icon *,
.button.tertiary:disabled .icon,
.button.tertiary:disabled .icon *,
.button.ghost:disabled .icon,
.button.ghost:disabled .icon *,
.button:disabled .icon,
.button:disabled .icon * {
  fill: currentColor !important;
  color: currentColor !important;
}

/* Remove hover states for disabled buttons with high specificity */
.button.primary:disabled:hover .icon,
.button.primary:disabled:hover .icon *,
.button.amazon-primary:disabled:hover .icon,
.button.amazon-primary:disabled:hover .icon *,
.button.secondary:disabled:hover .icon,
.button.secondary:disabled:hover .icon *,
.button.tertiary:disabled:hover .icon,
.button.tertiary:disabled:hover .icon *,
.button.ghost:disabled:hover .icon,
.button.ghost:disabled:hover .icon *,
.button:disabled:hover .icon,
.button:disabled:hover .icon * {
  fill: currentColor !important;
  color: currentColor !important;
}

.button.ghost:disabled {
  color: var(--text-text-interactive-disabled);
  cursor: not-allowed;
  opacity: 0.5;
}

/* Remove default focus outline */
.button:focus {
  outline: none;
  box-shadow: none;
}

/* Apply a hand cursor on hover for all buttons */
.button:hover {
  cursor: pointer;
}

/* Apply a "not-allowed" cursor for disabled buttons */
.button:disabled {
  cursor: not-allowed;
}