import React, { useState, useRef, useEffect } from 'react';
import styles from './AspectRatioPopover.module.css';

const aspectRatios = [
  { id: '1:1', label: '1:1', width: 48, height: 48 },
  { id: '4:3', label: '4:3', width: 66, height: 48 },
  { id: '3:2', label: '3:2', width: 56, height: 38 },
  { id: '16:9', label: '16:9', width: 67.556, height: 38 },
  { id: '9:16', label: '9:16', width: 38, height: 67.556 }
];

const AspectRatioPopover = ({ isOpen, onClose, onSelect, selectedRatio, buttonRef }) => {
  const popoverRef = useRef(null);
  const [position, setPosition] = useState({ left: 0, top: 0 });

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (popoverRef.current && !popoverRef.current.contains(event.target) &&
          buttonRef.current && !buttonRef.current.contains(event.target)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose, buttonRef]);

  useEffect(() => {
    if (isOpen && buttonRef.current) {
      const buttonRect = buttonRef.current.getBoundingClientRect();
      const popoverWidth = 288;
      const popoverHeight = 200;

      // Position below the button, centered horizontally
      let left = buttonRect.left + (buttonRect.width / 2) - (popoverWidth / 2);
      let top = buttonRect.bottom + 8;

      // Ensure popover doesn't go off screen
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      if (left < 8) left = 8;
      if (left + popoverWidth > viewportWidth - 8) {
        left = viewportWidth - popoverWidth - 8;
      }

      if (top + popoverHeight > viewportHeight - 8) {
        top = buttonRect.top - popoverHeight - 8;
      }

      setPosition({ left, top });
    }
  }, [isOpen, buttonRef]);

  if (!isOpen) return null;

  const handleOptionClick = (ratio) => {
    onSelect(ratio);
    onClose();
  };

  return (
    <div className={styles.popoverOverlay}>
      <div
        ref={popoverRef}
        className={styles.popover}
        style={{
          left: `${position.left}px`,
          top: `${position.top}px`
        }}
      >
        {aspectRatios.map((ratio) => {
          const isSelected = selectedRatio?.id === ratio.id;
          return (
            <div
              key={ratio.id}
              className={`${styles.aspectRatioOption} ${isSelected ? styles.selected : ''}`}
              onClick={() => handleOptionClick(ratio)}
            >
              <div
                className={styles.aspectRatioShape}
                style={{
                  width: `${ratio.width}px`,
                  height: `${ratio.height}px`
                }}
              >
                {ratio.label}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default AspectRatioPopover;
