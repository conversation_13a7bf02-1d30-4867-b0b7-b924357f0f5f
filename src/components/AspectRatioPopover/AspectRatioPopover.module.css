@import '../../styles/LUMINOUS_global_styles.css';

.popoverOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  pointer-events: none;
}

.popover {
  position: absolute;
  display: flex;
  width: 288px;
  height: 200px;
  padding: 20px;
  justify-content: center;
  align-items: flex-start;
  align-content: flex-start;
  gap: 16px;
  flex-wrap: wrap;
  border-radius: 8px;
  background: var(--ui-02, #1F252B);
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.50);
  pointer-events: auto;
  z-index: 1001;
}

.aspectRatioOption {
  width: 72px;
  height: 72px;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.aspectRatioShape {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  border-radius: 4px;
  border: 2px solid var(--ui-lines-02, #6D737D);
  background: transparent;
  color: var(--ui-interactive-02, #C8CDD5);
  font-feature-settings: 'liga' off, 'clig' off;
  font-family: "Amazon Ember";
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 150% */
  transition: all 0.2s ease;
}

.aspectRatioOption:hover .aspectRatioShape {
  border: 2px solid var(--ui-interactive-02-hover, #EBECEE);
  background: var(--ui-overlay, rgba(13, 17, 20, 0.55));
  color: var(--ui-interactive-02, #C8CDD5);
}

.aspectRatioOption.selected .aspectRatioShape {
  border: 2px solid var(--ui-interactive-01, #78AEFF);
  background: transparent;
  color: var(--text-interactive-01, #78AEFF);
}

.aspectRatioOption.selected:hover .aspectRatioShape {
  border: 2px solid var(--ui-interactive-01, #78AEFF);
  background: transparent;
  color: var(--text-interactive-01, #78AEFF);
}
