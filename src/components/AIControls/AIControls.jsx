import React, { useState } from 'react';
import styles from './AIControls.module.css';
import IconButtonToggle from '../IconButtonToggle/IconButtonToggle.jsx';
import TextField from '../TextField/TextField.jsx';
import TagButtonWithPopover from '../TagButtonWithPopover/TagButtonWithPopover.jsx';
import CustomizeToggle from '../CustomizeToggle/CustomizeToggle.jsx';
import ImageIntensitySlider from '../ImageIntensitySlider/ImageIntensitySlider.jsx';
import Button from '../Button/Button.jsx';
import NumberInput from '../NumberInput/NumberInput.jsx';
import FillSlider from '../FillSlider/FillSlider.jsx';
import {
  RestyledImageIcon,
  VideoAIIcon,
  NavGenAIIcon
} from '../../assets/IconLibrary.jsx';

// Custom icons for Style and Composition - using currentColor for proper inheritance
const StyleIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
    <path d="M18.2222 8.44444C19.2041 8.44444 20 9.24038 20 10.2222V18.2222C20 19.2041 19.2041 20 18.2222 20H10.2222C9.24038 20 8.44444 19.2041 8.44444 18.2222V15.5556H13.7778C14.7596 15.5556 15.5556 14.7596 15.5556 13.7778V8.44444H18.2222ZM13.7778 4C14.7596 4 15.5556 4.79594 15.5556 5.77778V8.44444H10.2222C9.24038 8.44444 8.44444 9.24038 8.44444 10.2222V15.5556H5.77778C4.79594 15.5556 4 14.7596 4 13.7778V5.77778C4 4.79594 4.79594 4 5.77778 4H13.7778Z" fill="currentColor"/>
  </svg>
);

const CompositionIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
    <path d="M4 21C3.44772 21 3 20.5523 3 20V17C3 16.4477 3.44772 16 4 16C4.55228 16 5 16.4477 5 17V19H7C7.55228 19 8 19.4477 8 20C8 20.5523 7.55228 21 7 21H4ZM17 21C16.4477 21 16 20.5523 16 20C16 19.4477 16.4477 19 17 19H19V17C19 16.4477 19.4477 16 20 16C20.5523 16 21 16.4477 21 17V20C21 20.5523 20.5523 21 20 21H17ZM4 8C3.44772 8 3 7.55228 3 7V4C3 3.44772 3.44772 3 4 3H7C7.55228 3 8 3.44772 8 4C8 4.55228 7.55228 5 7 5H5V7C5 7.55228 4.55228 8 4 8ZM20 8C19.4477 8 19 7.55228 19 7V5H17C16.4477 5 16 4.55228 16 4C16 3.44772 16.4477 3 17 3H20C20.5523 3 21 3.44772 21 4V7C21 7.55228 20.5523 8 20 8Z" fill="currentColor"/>
    <path d="M17 15.8889V8.11111C17 7.5 16.5 7 15.8889 7H8.11111C7.5 7 7 7.5 7 8.11111V15.8889C7 16.5 7.5 17 8.11111 17H15.8889C16.5 17 17 16.5 17 15.8889ZM10.2778 13.1L11.4444 14.5056L13.1667 12.2889C13.2778 12.1444 13.5 12.1444 13.6111 12.2944L15.5611 14.8944C15.7 15.0778 15.5667 15.3389 15.3389 15.3389H8.67778C8.44444 15.3389 8.31667 15.0722 8.46111 14.8889L9.84444 13.1111C9.95 12.9667 10.1611 12.9611 10.2778 13.1Z" fill="currentColor"/>
  </svg>
);

const AIControls = ({
  mode = 'image', // 'image' or 'video'
  onModeChange,
  onGenerate,
  className = ''
}) => {
  const [prompt, setPrompt] = useState('');
  const [aspectRatio, setAspectRatio] = useState('1:1');
  const [seed, setSeed] = useState(420530);
  const [isCustomizeEnabled, setIsCustomizeEnabled] = useState(false);
  const [sliderValues, setSliderValues] = useState({
    layoutFidelity: 50,
    colorBleed: 30,
    subjectBrightness: 70,
    backgroundBrightness: 40
  });

  // Sample menu items for the text field
  const samplePrompts = [
    { id: 'group1', type: 'group', label: 'Popular Styles' },
    { id: 'photorealistic', label: 'Photorealistic portrait' },
    { id: 'artistic', label: 'Artistic landscape' },
    { id: 'abstract', label: 'Abstract composition' },
    { id: 'group2', type: 'group', label: 'Moods' },
    { id: 'dramatic', label: 'Dramatic lighting' },
    { id: 'peaceful', label: 'Peaceful scene' },
    { id: 'energetic', label: 'High energy action' }
  ];

  const handleModeToggle = (index) => {
    const newMode = index === 0 ? 'image' : 'video';
    if (onModeChange) {
      onModeChange(newMode);
    }
  };

  const handleGenerate = () => {
    if (onGenerate) {
      onGenerate({
        mode,
        prompt,
        aspectRatio,
        seed,
        customizeOptions: isCustomizeEnabled ? sliderValues : null
      });
    }
  };

  const handleSliderChange = (sliderName) => (value) => {
    setSliderValues(prev => ({
      ...prev,
      [sliderName]: value
    }));
  };

  return (
    <div className={`${styles.container} ${isCustomizeEnabled ? styles.slidersExpanded : ''} ${className}`}>
      {/* Mode Toggle */}
      <div className={styles.modeToggle}>
        <IconButtonToggle
          icons={[RestyledImageIcon, VideoAIIcon]}
          layout="vertical"
          selectedIndex={mode === 'image' ? 0 : 1}
          onChange={handleModeToggle}
        />
      </div>

      {/* Main Controls Container */}
      <div className={styles.mainControls}>
        {/* Text Field and Controls */}
        <div className={styles.textFieldContainer}>
          <TextField
            placeholder="Describe an image to generate"
            value={prompt}
            onChange={setPrompt}
            showLabel={false}
            showOverflowMenu={true}
            overflowMenuItems={samplePrompts}
            multiSelect={false}
            showGroupLabels={true}
            className={styles.textField}
          />
          
          {/* Aspect Ratio and Icons Row */}
          <div className={styles.aspectRatioRow}>
            <div className={styles.aspectRatioContainer}>
              <TagButtonWithPopover initialRatio={aspectRatio} />
              <CustomizeToggle
                isSelected={isCustomizeEnabled}
                onToggle={setIsCustomizeEnabled}
              />
            </div>
          </div>
        </div>

        {/* Reference Images */}
        <div className={styles.referenceContainer}>
          <ImageIntensitySlider
            icon={StyleIcon}
            label="Style"
            onIntensityChange={(intensity) => console.log('Style intensity:', intensity)}
            onImageChange={(file, dataUrl) => console.log('Style image:', file?.name)}
          />
          <ImageIntensitySlider
            icon={CompositionIcon}
            label="Comp"
            onIntensityChange={(intensity) => console.log('Comp intensity:', intensity)}
            onImageChange={(file, dataUrl) => console.log('Comp image:', file?.name)}
          />
        </div>

        {/* Generate Button and Seed */}
        <div className={styles.generateContainer}>
          <Button
            variant="amazon-primary"
            icon={<NavGenAIIcon />}
            onClick={handleGenerate}
            className={styles.generateButton}
          >
            Generate
          </Button>
          <NumberInput
            label="Seed"
            initialValue={seed}
            min={0}
            max={999999}
            onChange={setSeed}
          />
        </div>

        {/* Sliders Container - slides out when customize is enabled */}
        <div className={`${styles.slidersContainer} ${isCustomizeEnabled ? styles.slidersVisible : ''}`}>
          <FillSlider
            initialValue={sliderValues.layoutFidelity}
            label="Layout fidelity"
            onChange={handleSliderChange('layoutFidelity')}
          />
          <FillSlider
            initialValue={sliderValues.colorBleed}
            label="Color bleed"
            onChange={handleSliderChange('colorBleed')}
          />
          <FillSlider
            initialValue={sliderValues.subjectBrightness}
            label="Subject brightness"
            onChange={handleSliderChange('subjectBrightness')}
          />
          <FillSlider
            initialValue={sliderValues.backgroundBrightness}
            label="Background brightness"
            onChange={handleSliderChange('backgroundBrightness')}
          />
        </div>
      </div>
    </div>
  );
};

export default AIControls;
