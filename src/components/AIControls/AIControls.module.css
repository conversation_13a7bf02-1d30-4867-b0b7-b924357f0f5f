@import '../../styles/LUMINOUS_global_styles.css';

/* AI Controls container */
.container {
  display: flex;
  width: 832px;
  height: 148px;
  padding: var(--Spacing-spacing00, 0px);
  align-items: flex-start;
  gap: var(--Spacing-spacing02, 8px);
  flex-shrink: 0;
  position: relative;
}

/* AI Controls container with sliders visible */
.container.slidersExpanded {
  width: 1015px;
}

/* Mode Toggle */
.modeToggle {
  flex-shrink: 0;
}

/* Main Controls Container */
.mainControls {
  display: flex;
  padding: var(--Spacing-spacing03, 12px);
  align-items: center;
  gap: 8px;
  flex: 1 0 0; /* Allow it to take available space */
  align-self: stretch;
  border-radius: var(--Spacing-spacing01, 4px);
  background: var(--UI-ui03, #2A3038);
  min-width: 0; /* Allow shrinking if needed */
}

/* Text Field + Actions container */
.textFieldContainer {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--Spacing-spacing02, 8px);
  flex: 1 0 0;
  align-self: stretch;
  min-width: 0;
  justify-content: space-between;
  width: 100%; /* Ensure it takes available space but doesn't change */
}

/* Text Field - stretch to fill container */
.textField {
  width: 100%;
  flex: 1 0 0; /* Allow text field to grow and fill available space */
  align-self: stretch;
}

/* Override TextField default dimensions when used in AIControls */
.textField :global(.textFieldContainer) {
  width: 100% !important;
}

/* More specific selector to override the default 100px height */
.container .textField :global(.inputField) {
  height: 92px !important; /* Fixed height as requested */
}

.container .textField :global(.inputField.default),
.container .textField :global(.inputField.hover),
.container .textField :global(.inputField.focused),
.container .textField :global(.inputField.filled) {
  height: 92px !important;
}

/* Aspect Ratio + Icons container */
.aspectRatioRow {
  display: flex;
  align-items: center;
  gap: 8px;
  align-self: stretch;
}

/* Aspect Ratio button + Customize icon container */
.aspectRatioContainer {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Reference image upload/intensity control container */
.referenceContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: var(--Spacing-spacing01, 4px);
  flex-shrink: 0;
}

/* Primary button + Number Input container */
.generateContainer {
  display: flex;
  width: 155px;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--Spacing-spacing02, 8px);
  align-self: stretch;
  flex-shrink: 0;
}

/* Amazon Primary Button with Icon */
.generateButton {
  display: flex;
  padding: 10px 16px;
  justify-content: center;
  align-items: center;
  gap: var(--Spacing-spacing01, 4px);
  flex: 1 0 0;
  align-self: stretch;
}

/* Sliders container */
.slidersContainer {
  display: none; /* Completely hidden when not visible */
  width: 175px;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--Spacing-spacing01, 4px);
  flex-shrink: 0;
  align-self: stretch;
  background: var(--UI-ui03, #2A3038);
  border-radius: var(--Spacing-spacing01, 4px);
  padding: var(--Spacing-spacing03, 12px);
}

.slidersContainer.slidersVisible {
  display: flex; /* Show when visible */
}

/* Fill Slider in sliders container */
.slidersContainer .fillSlider {
  display: flex;
  height: 24px;
  padding: var(--Spacing-spacing00, 0px) var(--Spacing-spacing02, 8px);
  align-items: center;
  gap: var(--Spacing-spacing02, 8px);
  align-self: stretch;
}
