@import '../../styles/LUMINOUS_global_styles.css';

.galleryItem {
  display: flex;
  width: 276px;
  height: 80px;
  flex-direction: column;
  align-items: flex-start;
  flex-shrink: 0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.thumbnailPromptContainer {
  display: flex;
  height: 80px;
  padding: 4px;
  align-items: flex-start;
  gap: 4px;
  align-self: stretch;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.promptContainer {
  display: flex;
  width: 192px;
  padding: 4px 8px;
  align-items: flex-start;
  gap: 8px;
  align-self: stretch;
}

.promptText {
  flex: 1 0 0;
  align-self: stretch;
  overflow: hidden;
  color: var(--text-01, #FFF);
  font-feature-settings: 'liga' off, 'clig' off;
  font-family: "Amazon Ember";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 142.857% */
  margin: 0;
  text-align: left;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  max-height: 60px; /* 3 lines × 20px line-height */
}

/* Default state - no additional styles needed */

/* Processing state */
.galleryItem.processing {
  /* Default processing state - no additional styles */
}

/* Hover state */
.galleryItem.hovered .thumbnailPromptContainer {
  background: var(--ui-hover-02, #0D1114);
}

/* Selected state */
.galleryItem.selected .thumbnailPromptContainer {
  border: 1px solid var(--ui-interactive-01, #78AEFF);
}

/* Hover + Processing state */
.galleryItem.hovered.processing .thumbnailPromptContainer {
  background: var(--ui-hover-02, #0D1114);
}

/* Selected + Processing state */
.galleryItem.selected.processing .thumbnailPromptContainer {
  border: 1px solid var(--ui-interactive-01, #78AEFF);
}

/* Selected + Hover state */
.galleryItem.selected.hovered .thumbnailPromptContainer {
  border: 1px solid var(--ui-interactive-01, #78AEFF);
  background: var(--ui-hover-02, #0D1114);
}

/* Selected + Hover + Processing state */
.galleryItem.selected.hovered.processing .thumbnailPromptContainer {
  border: 1px solid var(--ui-interactive-01, #78AEFF);
  background: var(--ui-hover-02, #0D1114);
}
