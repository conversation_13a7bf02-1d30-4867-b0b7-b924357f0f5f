import React, { useState } from 'react';
import ImageThumbnail from '../ImageThumbnail/ImageThumbnail.jsx';
import styles from './GalleryItem.module.css';

const GalleryItem = ({ 
  imageUrl, 
  prompt, 
  isProcessing = false,
  isSelected = false,
  onClick
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = () => {
    if (onClick) {
      onClick();
    }
  };

  const getItemClasses = () => {
    let classes = [styles.galleryItem];
    
    if (isProcessing) classes.push(styles.processing);
    if (isSelected) classes.push(styles.selected);
    if (isHovered) classes.push(styles.hovered);
    
    return classes.join(' ');
  };

  return (
    <div
      className={getItemClasses()}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={handleClick}
    >
      <div className={styles.thumbnailPromptContainer}>
        <ImageThumbnail
          imageUrl={imageUrl}
          isProcessing={isProcessing}
        />

        <div className={styles.promptContainer}>
          <p className={styles.promptText}>
            {prompt || "Generating..."}
          </p>
        </div>
      </div>
    </div>
  );
};

export default GalleryItem;
