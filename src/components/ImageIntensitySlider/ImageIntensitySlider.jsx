import React, { useState, useRef } from 'react';
import styles from './ImageIntensitySlider.module.css';

const ImageIntensitySlider = ({
  onIntensityChange,
  onImageChange,
  icon: CustomIcon,
  label = "Style"
}) => {
  const [image, setImage] = useState(null);
  const [intensity, setIntensity] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef(null);
  const containerRef = useRef(null);

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImage(e.target.result);
        if (onImageChange) {
          onImageChange(file, e.target.result);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleDeleteImage = (e) => {
    e.stopPropagation();
    e.preventDefault();
    console.log('Delete button clicked'); // Debug log
    setImage(null);
    setIntensity(0);
    if (onImageChange) {
      onImageChange(null, null);
    }
    if (onIntensityChange) {
      onIntensityChange(0);
    }
  };

  const handleMouseDown = (e) => {
    if (!image) return;
    e.preventDefault();
    setIsDragging(true);
    updateIntensity(e);
  };

  const handleMouseMove = (e) => {
    if (!isDragging || !image) return;
    updateIntensity(e);
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const updateIntensity = (e) => {
    if (!containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const width = rect.width;

    // Calculate intensity from left to right (0 at left, 1 at right)
    let newIntensity = Math.max(0, Math.min(1, x / width));

    // Round to nearest 0.1
    newIntensity = Math.round(newIntensity * 10) / 10;

    setIntensity(newIntensity);
    if (onIntensityChange) {
      onIntensityChange(newIntensity);
    }
  };

  // Add global mouse event listeners when dragging
  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging]);

  const StyleIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path d="M18.2222 8.44444C19.2041 8.44444 20 9.24038 20 10.2222V18.2222C20 19.2041 19.2041 20 18.2222 20H10.2222C9.24038 20 8.44444 19.2041 8.44444 18.2222V15.5556H13.7778C14.7596 15.5556 15.5556 14.7596 15.5556 13.7778V8.44444H18.2222ZM13.7778 4C14.7596 4 15.5556 4.79594 15.5556 5.77778V8.44444H10.2222C9.24038 8.44444 8.44444 9.24038 8.44444 10.2222V15.5556H5.77778C4.79594 15.5556 4 14.7596 4 13.7778V5.77778C4 4.79594 4.79594 4 5.77778 4H13.7778Z" fill="currentColor"/>
    </svg>
  );

  const CloseIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path d="M12 4.5C7.8525 4.5 4.5 7.8525 4.5 12C4.5 16.1475 7.8525 19.5 12 19.5C16.1475 19.5 19.5 16.1475 19.5 12C19.5 7.8525 16.1475 4.5 12 4.5ZM15.2175 14.1675C15.51 14.46 15.51 14.9325 15.2175 15.225C14.925 15.5175 14.4525 15.5175 14.16 15.225L12 13.0575L9.8325 15.225C9.54 15.5175 9.0675 15.5175 8.775 15.225C8.4825 14.9325 8.4825 14.46 8.775 14.1675L10.9425 12L8.7825 9.8325C8.49 9.54 8.49 9.0675 8.7825 8.775C9.075 8.4825 9.5475 8.4825 9.84 8.775L12 10.9425L14.1675 8.775C14.46 8.4825 14.9325 8.4825 15.225 8.775C15.5175 9.0675 15.5175 9.54 15.225 9.8325L13.0575 12L15.2175 14.1675Z" fill="#C8CDD5"/>
    </svg>
  );

  if (!image) {
    return (
      <div className={styles.container}>
        <button
          className={`${styles.uploadButton} ${isHovered ? styles.hovered : ''}`}
          onClick={handleUploadClick}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          {CustomIcon ? <CustomIcon /> : <StyleIcon />}
          <span className={styles.uploadText}>{label}</span>
        </button>
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          style={{ display: 'none' }}
        />
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div
        ref={containerRef}
        className={`${styles.imageContainer} ${isHovered ? styles.imageHovered : ''}`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onMouseDown={handleMouseDown}
      >
        <div 
          className={styles.image}
          style={{ backgroundImage: `url(${image})` }}
        />
        
        <div
          className={styles.intensityOverlay}
          style={{ width: `${(1 - intensity) * 100}%` }}
        />
        
        <div className={styles.intensityText}>
          {intensity.toFixed(1)}
        </div>
        
        {isHovered && (
          <button
            className={styles.deleteButton}
            onClick={handleDeleteImage}
            onMouseDown={(e) => e.stopPropagation()}
            onMouseUp={(e) => e.stopPropagation()}
          >
            <CloseIcon />
          </button>
        )}
      </div>
    </div>
  );
};

export default ImageIntensitySlider;
