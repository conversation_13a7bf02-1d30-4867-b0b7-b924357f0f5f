@import '../../styles/LUMINOUS_global_styles.css';

.container {
  display: inline-block;
}

/* Upload Button Styles */
.uploadButton {
  display: flex;
  width: 60px;
  height: 60px;
  padding: 0px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0px;
  flex-shrink: 0;
  border-radius: 4px;
  border: 1px solid var(--ui-lines-01, #383E46);
  background: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.uploadButton.hovered {
  border: 1px solid var(--ui-interactive-01-hover, #4F96FF);
}

.uploadText {
  color: #FFF;
  font-feature-settings: 'liga' off, 'clig' off;
  font-family: "Amazon Ember";
  font-size: 10px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  margin-top: 2px;
}

.uploadButton.hovered .uploadText {
  color: var(--text-interactive-01-hover, #4F96FF);
}

/* Image Container Styles */
.imageContainer {
  position: relative;
  width: 60px;
  height: 60px;
  flex-shrink: 0;
  border-radius: 4px;
  border: 1px solid var(--ui-lines-01, #383E46);
  overflow: visible;
  cursor: pointer;
  transition: all 0.2s ease;
}

.imageContainer.imageHovered {
  border-radius: 4px;
  border: 1px solid var(--ui-interactive-02-hover, #EBECEE);
}

.image {
  width: 60px;
  height: 60px;
  flex-shrink: 0;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-color: lightgray;
  border-radius: 4px;
  overflow: hidden;
}

/* Intensity Overlay */
.intensityOverlay {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  background: var(--ui-03-overlay, rgba(13, 17, 20, 0.55));
  transition: width 0.1s ease;
  pointer-events: none;
}

/* Intensity Text */
.intensityText {
  position: absolute;
  bottom: 4px;
  left: 50%;
  transform: translateX(-50%);
  color: #FFF;
  text-align: center;
  font-feature-settings: 'liga' off, 'clig' off;
  font-family: "Amazon Ember";
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
  line-height: 20px; /* 142.857% */
  pointer-events: none;
  z-index: 2;
}

/* Delete Button */
.deleteButton {
  position: absolute;
  top: -12px;
  right: -12px;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  background: none;
  border: none;
  cursor: pointer;
  z-index: 10;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.deleteButton:hover {
  transform: scale(1.1);
}

.deleteButton svg {
  filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.8));
}
