@import '../../styles/LUMINOUS_global_styles.css';

.sessionCard {
  display: inline-flex;
  padding: 4px;
  align-items: flex-start;
  gap: 4px;
  border-radius: 8px;
  background: var(--ui-02, #1F252B);
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.sessionCard.hovered {
  display: flex;
  width: 222px;
  background: var(--ui-hover-02, #0D1114);
  position: relative;
  z-index: 10;
}

.sessionCard.active {
  border: 2px solid var(--ui-interactive-01, #78AEFF);
  background: var(--ui-selected-01, #000E28);
  padding: 2px; /* Adjust padding to account for border */
}

.sessionCard.active.hovered {
  border: 2px solid var(--ui-interactive-01-hover, #4F96FF);
  background: var(--ui-hover-02, #0D1114);
  position: relative;
  z-index: 10;
}

.imageThumbnail {
  width: 72px;
  height: 72px;
  flex-shrink: 0;
  border-radius: 4px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-color: lightgray;
}

.sessionInfo {
  display: flex;
  width: 142px;
  align-items: center;
  gap: 0px;
  flex-shrink: 0;
  align-self: stretch;
}

.dateContainer {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex: 1 0 0;
}

.dateText {
  align-self: stretch;
  color: var(--text-02, #C8CDD5);
  font-feature-settings: 'liga' off, 'clig' off;
  font-family: "Amazon Ember";
  font-size: 14px;
  font-style: italic;
  font-weight: 300;
  line-height: 20px; /* 142.857% */
  text-align: left;
}

.deleteButton {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.2s ease;
}

.deleteButton:hover {
  opacity: 0.7;
}

.deleteButton svg {
  width: 24px;
  height: 24px;
}
