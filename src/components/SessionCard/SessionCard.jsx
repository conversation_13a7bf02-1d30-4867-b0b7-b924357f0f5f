import React, { useState } from 'react';
import styles from './SessionCard.module.css';

const SessionCard = ({ 
  imageUrl, 
  createdDate, 
  isActive = false, 
  onDelete, 
  onClick 
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const formatDate = (date) => {
    const options = { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    };
    return new Date(date).toLocaleDateString('en-US', options);
  };

  const handleDeleteClick = (e) => {
    e.stopPropagation(); // Prevent card click when delete is clicked
    if (onDelete) {
      onDelete();
    }
  };

  const DeleteIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path d="M7.5 17.25C7.5 18.075 8.175 18.75 9 18.75H15C15.825 18.75 16.5 18.075 16.5 17.25V9.75C16.5 8.925 15.825 8.25 15 8.25H9C8.175 8.25 7.5 8.925 7.5 9.75V17.25ZM16.5 6H14.625L14.0925 5.4675C13.9575 5.3325 13.7625 5.25 13.5675 5.25H10.4325C10.2375 5.25 10.0425 5.3325 9.9075 5.4675L9.375 6H7.5C7.0875 6 6.75 6.3375 6.75 6.75C6.75 7.1625 7.0875 7.5 7.5 7.5H16.5C16.9125 7.5 17.25 7.1625 17.25 6.75C17.25 6.3375 16.9125 6 16.5 6Z" fill="#C8CDD5"/>
    </svg>
  );

  const cardClasses = `${styles.sessionCard} ${isActive ? styles.active : ''} ${isHovered ? styles.hovered : ''}`;

  return (
    <div 
      className={cardClasses}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onClick}
    >
      <div 
        className={styles.imageThumbnail}
        style={{ 
          backgroundImage: imageUrl ? `url(${imageUrl})` : 'none',
          backgroundColor: imageUrl ? 'transparent' : 'lightgray'
        }}
      />
      
      {isHovered && (
        <div className={styles.sessionInfo}>
          <div className={styles.dateContainer}>
            <div className={styles.dateText}>
              {formatDate(createdDate)}
            </div>
          </div>
          <button 
            className={styles.deleteButton}
            onClick={handleDeleteClick}
            aria-label="Delete session"
          >
            <DeleteIcon />
          </button>
        </div>
      )}
    </div>
  );
};

export default SessionCard;
