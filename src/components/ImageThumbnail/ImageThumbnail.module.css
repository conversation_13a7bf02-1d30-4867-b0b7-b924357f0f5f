@import '../../styles/LUMINOUS_global_styles.css';

.thumbnailContainer {
  display: flex;
  width: 72px;
  height: 72px;
  justify-content: center;
  align-items: center;
  gap: 0px;
  flex-shrink: 0;
  border-radius: 8px;
  border: 1px solid var(--ui-lines-01, #383E46);
  position: relative;
  overflow: hidden;
}

.imageThumbnail {
  width: 72px;
  height: 72px;
  flex-shrink: 0;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-color: lightgray;
}

/* Processing state styles */
.thumbnailContainer:has(.imageOverlay) .imageThumbnail {
  filter: blur(2px);
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 72px;
  height: 72px;
  flex-shrink: 0;
  aspect-ratio: 1/1;
  background: rgba(13, 17, 20, 0.75);
  display: flex;
  justify-content: center;
  align-items: center;
}

.spinner {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
