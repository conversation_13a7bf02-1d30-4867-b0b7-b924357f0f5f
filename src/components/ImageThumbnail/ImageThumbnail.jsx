import React from 'react';
import styles from './ImageThumbnail.module.css';

const ImageThumbnail = ({ 
  imageUrl, 
  isProcessing = false,
  alt = "Generated image"
}) => {
  const Spinner = () => (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      width="16" 
      height="16" 
      viewBox="0 0 16 16" 
      fill="none"
      className={styles.spinner}
    >
      <g clipPath="url(#clip0_465_5035)">
        <circle cx="8" cy="8" r="7" stroke="#6D737D" strokeWidth="2"/>
        <path 
          d="M8 1C9.31593 1 10.6052 1.37093 11.7199 2.07024C12.8347 2.76955 13.7297 3.76891 14.3024 4.95369C14.8751 6.13847 15.1022 7.46068 14.9577 8.76865C14.8132 10.0766 14.3029 11.3174 13.4854 12.3486" 
          stroke="#EBECEE" 
          strokeWidth="2" 
          strokeLinecap="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_465_5035">
          <rect width="16" height="16" fill="white"/>
        </clipPath>
      </defs>
    </svg>
  );

  if (isProcessing) {
    return (
      <div className={styles.thumbnailContainer}>
        <div 
          className={styles.imageThumbnail}
          style={{ 
            backgroundImage: imageUrl ? `url(${imageUrl})` : 'none',
            backgroundColor: imageUrl ? 'transparent' : 'lightgray'
          }}
        />
        <div className={styles.imageOverlay}>
          <Spinner />
        </div>
      </div>
    );
  }

  return (
    <div className={styles.thumbnailContainer}>
      <div 
        className={styles.imageThumbnail}
        style={{ 
          backgroundImage: imageUrl ? `url(${imageUrl})` : 'none',
          backgroundColor: imageUrl ? 'transparent' : 'lightgray'
        }}
      />
    </div>
  );
};

export default ImageThumbnail;
