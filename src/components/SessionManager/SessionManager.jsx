import React, { useState, useRef } from 'react';
import Button from '../Button/Button.jsx';
import SessionCard from '../SessionCard/SessionCard.jsx';
import styles from './SessionManager.module.css';

const SessionManager = () => {
  const [sessions, setSessions] = useState([]);
  const [activeSessionId, setActiveSessionId] = useState(null);
  const fileInputRef = useRef(null);

  const handleNewSessionClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
      const imageUrl = URL.createObjectURL(file);
      const newSession = {
        id: Date.now(),
        imageUrl,
        createdDate: new Date(),
        fileName: file.name
      };
      
      setSessions(prev => [...prev, newSession]);
      setActiveSessionId(newSession.id);
    }
    
    // Reset file input
    event.target.value = '';
  };

  const handleSessionClick = (sessionId) => {
    setActiveSessionId(sessionId);
  };

  const handleDeleteSession = (sessionId) => {
    setSessions(prev => {
      const updated = prev.filter(session => session.id !== sessionId);
      
      // If we deleted the active session, clear active state
      if (sessionId === activeSessionId) {
        setActiveSessionId(null);
      }
      
      // Clean up object URL to prevent memory leaks
      const sessionToDelete = prev.find(session => session.id === sessionId);
      if (sessionToDelete?.imageUrl) {
        URL.revokeObjectURL(sessionToDelete.imageUrl);
      }
      
      return updated;
    });
  };

  return (
    <div className={styles.sessionManager}>
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileUpload}
        style={{ display: 'none' }}
      />
      
      <div className={styles.sessionsContainer}>
        <Button variant="new" onClick={handleNewSessionClick} />
        
        {sessions.map(session => (
          <SessionCard
            key={session.id}
            imageUrl={session.imageUrl}
            createdDate={session.createdDate}
            isActive={session.id === activeSessionId}
            onClick={() => handleSessionClick(session.id)}
            onDelete={() => handleDeleteSession(session.id)}
          />
        ))}
      </div>
    </div>
  );
};

export default SessionManager;
