@import '../../styles/LUMINOUS_global_styles.css';

.container {
  display: inline-block;
}

.ratioGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: var(--Spacing-spacing01, 4px);
  width: 120px;
  height: 80px;
}

.ratioButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2px;
  padding: 4px;
  border: 1px solid var(--ui-lines-01, #383E46);
  border-radius: 4px;
  background: var(--ui-03, #2A3038);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.ratioButton:hover,
.ratioButton.hovered {
  border: 1px solid var(--ui-interactive-02-hover, #EBECEE);
  background: var(--ui-hover-02, #0D1114);
}

.ratioButton.selected {
  border: 1px solid var(--ui-interactive-01, #78AEFF);
  background: var(--ui-hover-03, rgba(127, 133, 143, 0.2));
}

.ratioButton.selected:hover,
.ratioButton.selected.hovered {
  border: 1px solid var(--ui-interactive-01-hover, #4F96FF);
}

.ratioShape {
  width: 16px;
  height: 16px;
  max-width: 20px;
  max-height: 12px;
  border: 1px solid var(--ui-interactive-02, #C8CDD5);
  border-radius: 2px;
  background: transparent;
  flex-shrink: 0;
}

.ratioButton:hover .ratioShape,
.ratioButton.hovered .ratioShape {
  border-color: var(--ui-interactive-02-hover, #EBECEE);
}

.ratioButton.selected .ratioShape {
  border-color: var(--ui-interactive-01, #78AEFF);
  background: var(--ui-interactive-01, #78AEFF);
}

.ratioButton.selected:hover .ratioShape,
.ratioButton.selected.hovered .ratioShape {
  border-color: var(--ui-interactive-01-hover, #4F96FF);
  background: var(--ui-interactive-01-hover, #4F96FF);
}

.ratioLabel {
  color: var(--text-02, #C8CDD5);
  font-family: "Amazon Ember";
  font-size: 8px;
  font-weight: 500;
  line-height: 10px;
  text-align: center;
  white-space: nowrap;
}

.ratioButton:hover .ratioLabel,
.ratioButton.hovered .ratioLabel {
  color: var(--text-interactive-02-hover, #EBECEE);
}

.ratioButton.selected .ratioLabel {
  color: var(--text-interactive-01, #78AEFF);
}

.ratioButton.selected:hover .ratioLabel,
.ratioButton.selected.hovered .ratioLabel {
  color: var(--text-interactive-01-hover, #4F96FF);
}
