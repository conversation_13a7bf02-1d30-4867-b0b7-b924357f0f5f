import React, { useState } from 'react';
import styles from './AspectRatioSelector.module.css';

const AspectRatioSelector = ({ 
  selectedRatio = '1:1', 
  onRatioChange,
  className = ''
}) => {
  const [hoveredRatio, setHoveredRatio] = useState(null);

  const aspectRatios = [
    { id: '1:1', label: '1:1', width: 1, height: 1 },
    { id: '4:3', label: '4:3', width: 4, height: 3 },
    { id: '3:2', label: '3:2', width: 3, height: 2 },
    { id: '16:9', label: '16:9', width: 16, height: 9 },
    { id: '9:16', label: '9:16', width: 9, height: 16 },
    { id: '2:3', label: '2:3', width: 2, height: 3 }
  ];

  const handleRatioClick = (ratio) => {
    if (onRatioChange) {
      onRatioChange(ratio.id);
    }
  };

  const getRatioClasses = (ratio) => {
    const isSelected = selectedRatio === ratio.id;
    const isHovered = hoveredRatio === ratio.id;
    
    return `${styles.ratioButton} ${isSelected ? styles.selected : ''} ${isHovered ? styles.hovered : ''}`;
  };

  return (
    <div className={`${styles.container} ${className}`}>
      <div className={styles.ratioGrid}>
        {aspectRatios.map((ratio) => (
          <button
            key={ratio.id}
            className={getRatioClasses(ratio)}
            onClick={() => handleRatioClick(ratio)}
            onMouseEnter={() => setHoveredRatio(ratio.id)}
            onMouseLeave={() => setHoveredRatio(null)}
            aria-label={`Select ${ratio.label} aspect ratio`}
          >
            <div 
              className={styles.ratioShape}
              style={{
                aspectRatio: `${ratio.width} / ${ratio.height}`
              }}
            />
            <span className={styles.ratioLabel}>{ratio.label}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default AspectRatioSelector;
