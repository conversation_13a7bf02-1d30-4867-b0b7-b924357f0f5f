# SelectMenu Component

A versatile dropdown menu component with support for single/multi-select, group labels, and custom scrollbar styling.

## Features

- **Single/Multi-Select**: Toggle between single and multi-selection modes
- **Group Labels**: Optional group headers for organizing menu items
- **5 Item States**: default, hover, selected, selected-hover, and disabled
- **Custom Scrollbar**: Consistent scrollbar styling matching the design system
- **Click Outside**: Automatically closes when clicking outside the menu
- **Keyboard Accessible**: Full keyboard navigation support

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `isOpen` | `boolean` | `false` | Whether the menu is visible |
| `onClose` | `function` | `undefined` | Callback when menu should close |
| `items` | `array` | `[]` | Array of menu items |
| `selectedItems` | `array` | `[]` | Array of currently selected items |
| `onSelectionChange` | `function` | `undefined` | Callback when selection changes |
| `multiSelect` | `boolean` | `false` | Enable multi-selection mode |
| `showGroupLabels` | `boolean` | `false` | Show group label items |
| `position` | `object` | `{left: 0, top: 0}` | Menu position coordinates |
| `triggerRef` | `ref` | `undefined` | Reference to trigger element |

## Item Structure

### Regular Menu Item
```javascript
{
  id: 'unique-id',
  label: 'Display Text',
  disabled: false // optional
}
```

### Group Label Item
```javascript
{
  id: 'group-id',
  type: 'group',
  label: 'Group Title'
}
```

## Usage

```jsx
import SelectMenu from './components/SelectMenu/SelectMenu';

const menuItems = [
  { id: 'group1', type: 'group', label: 'Common Options' },
  { id: 'option1', label: 'First Option' },
  { id: 'option2', label: 'Second Option' },
  { id: 'option3', label: 'Disabled Option', disabled: true }
];

// Single select
<SelectMenu
  isOpen={isMenuOpen}
  onClose={() => setIsMenuOpen(false)}
  items={menuItems}
  selectedItems={selectedItems}
  onSelectionChange={setSelectedItems}
  multiSelect={false}
  showGroupLabels={true}
  position={{ left: 100, top: 200 }}
  triggerRef={buttonRef}
/>

// Multi select
<SelectMenu
  isOpen={isMenuOpen}
  onClose={() => setIsMenuOpen(false)}
  items={menuItems}
  selectedItems={selectedItems}
  onSelectionChange={setSelectedItems}
  multiSelect={true}
  showGroupLabels={true}
  position={{ left: 100, top: 200 }}
  triggerRef={buttonRef}
/>
```

## Design Specifications

- **Container**: 200px width, 215px height, 4px padding
- **Background**: Dark background (#1F252B) with shadow
- **Items**: 4px 16px padding, Amazon Ember 14px font
- **Group Labels**: Bold font weight (700), white text
- **Multi-select**: Check icon for selected items
- **Scrollbar**: Custom 8px width with design system colors

## States

### Default
- Gray text (#C8CDD5)
- No background

### Hover
- Light gray text (#EBECEE)
- Dark background (#0D1114)

### Selected
- Blue text (#78AEFF)
- Check icon (multi-select only)

### Selected-Hover
- Blue text (#78AEFF)
- Dark background (#0D1114)
- Check icon (multi-select only)

### Disabled
- Disabled text color (#505760)
- No hover effects
- Not clickable
