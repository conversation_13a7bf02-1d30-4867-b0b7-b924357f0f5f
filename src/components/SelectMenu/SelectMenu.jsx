import React, { useState, useRef, useEffect } from 'react';
import ScrollableContainer from '../ScrollableContainer/ScrollableContainer';
import styles from './SelectMenu.module.css';

const CheckIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
    <path d="M13.7504 6.68751L8.45039 11.9875L6.23789 9.77501C5.99414 9.53126 5.60039 9.53126 5.35664 9.77501C5.11289 10.0188 5.11289 10.4125 5.35664 10.6563L8.00664 13.3063C8.25039 13.55 8.64414 13.55 8.88789 13.3063L14.6254 7.56876C14.8691 7.32501 14.8691 6.92501 14.6191 6.68126C14.3879 6.44376 13.9941 6.44376 13.7504 6.68751Z" fill="var(--UI-uiInteractive01, #78AEFF)"/>
  </svg>
);

const SelectMenu = ({ 
  isOpen, 
  onClose, 
  items = [], 
  selectedItems = [], 
  onSelectionChange,
  multiSelect = false,
  showGroupLabels = false,
  position = { left: 0, top: 0 },
  triggerRef
}) => {
  const menuRef = useRef(null);
  const [hoveredItem, setHoveredItem] = useState(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target) &&
          triggerRef.current && !triggerRef.current.contains(event.target)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose, triggerRef]);

  const handleItemClick = (item) => {
    if (item.disabled) return;

    if (multiSelect) {
      const isSelected = selectedItems.some(selected => selected.id === item.id);
      let newSelection;

      if (isSelected) {
        newSelection = selectedItems.filter(selected => selected.id !== item.id);
      } else {
        newSelection = [...selectedItems, item];
      }

      onSelectionChange(newSelection);
    } else {
      // Single select: toggle selection
      const isSelected = selectedItems.some(selected => selected.id === item.id);

      if (isSelected) {
        // Deselect if already selected
        onSelectionChange([]);
      } else {
        // Select the item
        onSelectionChange([item]);
      }

      onClose();
    }
  };

  const isItemSelected = (item) => {
    return selectedItems.some(selected => selected.id === item.id);
  };

  const getItemState = (item) => {
    if (item.disabled) return 'disabled';
    
    const isSelected = isItemSelected(item);
    const isHovered = hoveredItem === item.id;
    
    if (isSelected && isHovered) return 'selected-hover';
    if (isSelected) return 'selected';
    if (isHovered) return 'hover';
    return 'default';
  };

  const renderItem = (item, index) => {
    if (item.type === 'group' && showGroupLabels) {
      return (
        <div key={`group-${index}`} className={styles.groupLabel}>
          {item.label}
        </div>
      );
    }

    const state = getItemState(item);
    const itemClasses = [
      styles.menuItem,
      styles[state],
      multiSelect ? styles.multiSelect : styles.singleSelect
    ].filter(Boolean).join(' ');

    return (
      <div
        key={item.id}
        className={itemClasses}
        onClick={() => handleItemClick(item)}
        onMouseEnter={() => setHoveredItem(item.id)}
        onMouseLeave={() => setHoveredItem(null)}
      >
        {multiSelect ? (
          <>
            <div className={styles.textContainer}>
              <span className={styles.itemText}>{item.label}</span>
            </div>
            <div className={styles.iconContainer}>
              {isItemSelected(item) && <CheckIcon />}
            </div>
          </>
        ) : (
          <span className={styles.itemText}>{item.label}</span>
        )}
      </div>
    );
  };

  if (!isOpen) return null;

  return (
    <div className={styles.menuOverlay}>
      <div
        ref={menuRef}
        className={styles.selectMenuContainer}
        style={{
          left: `${position.left}px`,
          top: `${position.top}px`
        }}
      >
        <ScrollableContainer className={styles.menuItemsList}>
          {items.map((item, index) => renderItem(item, index))}
        </ScrollableContainer>
      </div>
    </div>
  );
};

export default SelectMenu;
