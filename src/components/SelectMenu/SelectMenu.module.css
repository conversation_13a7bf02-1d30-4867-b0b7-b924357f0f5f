@import '../../styles/LUMINOUS_global_styles.css';

/* Menu overlay */
.menuOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  pointer-events: none;
}

/* Select menu container */
.selectMenuContainer {
  position: absolute;
  display: flex;
  width: 200px;
  height: 215px;
  padding: 4px 0px;
  align-items: flex-start;
  
  border-radius: 4px;
  background: var(--UI-ui02, #1F252B);
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.40);
  pointer-events: auto;
  z-index: 1001;
}

/* Menu items list container */
.menuItemsList {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex: 1 0 0;
}

/* Group label */
.groupLabel {
  display: flex;
  padding: var(--Spacing-spacing01, 4px) var(--Spacing-spacing04, 16px);
  align-items: center;
  gap: var(--Spacing-spacing00, 0px);
  align-self: stretch;
  
  color: var(--Text-text01, #FFF);
  font-feature-settings: 'liga' off, 'clig' off;
  
  /* Title */
  font-family: "Amazon Ember";
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
  line-height: 20px; /* 142.857% */
}

/* Base menu item styles */
.menuItem {
  display: flex;
  padding: var(--Spacing-spacing01, 4px) var(--Spacing-spacing04, 16px);
  align-items: flex-start;
  gap: var(--Spacing-spacing00, 0px);
  align-self: stretch;
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease;
}

/* Single select menu item */
.singleSelect {
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

/* Multi select menu item */
.multiSelect {
  flex-direction: row;
  align-items: center;
}

/* Item text */
.itemText {
  font-feature-settings: 'liga' off, 'clig' off;
  font-family: "Amazon Ember";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 142.857% */
  text-align: left;
}

/* Text container for multiselect */
.textContainer {
  display: flex;
  padding: var(--Spacing-spacing00, 0px);
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: var(--Spacing-spacing00, 0px);
  flex: 1 0 0;
}

.textContainer .itemText {
  align-self: flex-start;
  text-align: left;
}

/* Icon container for multiselect */
.iconContainer {
  display: flex;
  width: 20px;
  height: 20px;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

/* Default state */
.menuItem.default .itemText {
  color: var(--Text-textInteractive02, #C8CDD5);
}

/* Hover state */
.menuItem.hover {
  background: var(--UI-ui02Hover, #0D1114);
}

.menuItem.hover .itemText {
  color: var(--Text-textInteractive02Hover, #EBECEE);
}

/* Selected state */
.menuItem.selected .itemText {
  color: var(--Text-textInteractive01, #78AEFF);
}

/* Selected-hover state */
.menuItem.selected-hover {
  background: var(--UI-ui02Hover, #0D1114);
}

.menuItem.selected-hover .itemText {
  color: var(--Text-textInteractive01, #78AEFF);
}

/* Disabled state */
.menuItem.disabled {
  cursor: not-allowed;
}

.menuItem.disabled .itemText {
  color: var(--Text-textInteractiveDisabled, #505760);
}
