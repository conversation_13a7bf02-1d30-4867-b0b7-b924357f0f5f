/* Default icon style - for standalone icons not in buttons */
.icon {
  background: transparent;
  width: 24px;
  height: 24px;
  display: inline-block;
  vertical-align: middle;
  fill: currentColor;
  color: inherit;
}

/* Button icon styles are now handled in Button.module.css for better specificity */
/* This file is kept for standalone icon usage */

/* Optional: for icon-only buttons */
.button.icon-only {
  padding: 8px;
  width: 40px;
  height: 40px;
  justify-content: center;
}

.button.icon-only .icon {
  margin: 0;
}