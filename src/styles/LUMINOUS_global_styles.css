:root {
    /* --------------------- STATUS COLORS --------------------- */
    --status-error: #F74D52FF;
    --status-error-hover: #E00007FF;
    --status-informational: #4F96FFFF;
    --status-neutral: #A2A8B2FF;
    --status-notification: #FF5F8AFF;
    --status-success: #8BD742FF;
    --status-success-hover: #55BE24FF;
    --status-warning: #F7941FFF;
    --status-warning-hover: #F18302FF;

    /* --------------------- TEXT COLORS --------------------- */
    --text-01: #FFFFFFFF;
    --text-02: #C8CDD5FF;
    --text-03: #0D1114FF;
    --text-interactive-01: #78AEFFFF;
    --text-interactive-01-hover: #4F96FFFF;
    --text-interactive-02: #C8CDD5FF;
    --text-interactive-02-hover: #EBECEEFF;
    --text-interactive-disabled: #505760FF;

    /* --------------------- UI COLORS --------------------- */
    --ui-01: #0D1114FF;
    --ui-02: #1F252BFF;
    --ui-03: #2A3038FF;
    --ui-04: #383E46FF;
    --ui-05: #242A31FF;
    --ui-lines-01: #383E46FF;
    --ui-lines-02: #6D737DFF;
    --ui-interactive-01: #78AEFFFF;
    --ui-interactive-01-hover: #4F96FFFF;
    --ui-interactive-02: #C8CDD5FF;
    --ui-interactive-02-hover: #EBECEEFF;
    --ui-interactive-03: #FF9900FF;
    --ui-interactive-03-hover: #F18302FF;
    --ui-interactive-disabled: #505760FF;
    --ui-hover-01: #1F252BFF;
    --ui-hover-02: #0D1114FF;
    --ui-hover-03: rgba(127, 133, 143, 0.2);
    --ui-selected-01: #000E28FF;
    --ui-selected-02: #78AEFFFF;
    --ui-overlay: rgba(13, 17, 20, 0.55);

    
    /* --------------------- TYPOGRAPHY --------------------- */
    --font-primary: "Amazon Ember", sans-serif;

    /* Headings */
    --h1-size: 40px; --h1-weight: 300; --h1-line-height: 56px;
    --h2-size: 32px; --h2-weight: 300; --h2-line-height: 44px;
    --h3-size: 28px; --h3-weight: 300; --h3-line-height: 40px;
    --h4-size: 24px; --h4-weight: 300; --h4-line-height: 32px;
    --h5-size: 20px; --h5-weight: 700; --h5-line-height: 28px;
    --h6-size: 16px; --h6-weight: 400; --h6-line-height: 24px;
    --h7-size: 16px; --h7-weight: 700; --h7-line-height: 24px;

    /* Title */
    --title-size: 14px; --title-weight: 700; --title-line-height: 20px;

    /* Body Text */
    --body-1-size: 14px; --body-1-weight: 400; --body-1-line-height: 20px;
    --body-2-size: 14px; --body-2-weight: 300; --body-2-line-height: 20px; --body-2-style: italic;
    --body-underline-size: 14px; --body-underline-weight: 400; --body-underline-line-height: 20px; --body-underline-decoration: underline;
    --body-strikethrough-size: 14px; --body-strikethrough-weight: 400; --body-strikethrough-line-height: 20px; --body-strikethrough-decoration: line-through;

    /* Labels */
    --label-1-size: 12px; --label-1-weight: 500; --label-1-line-height: 16px;
    --label-2-size: 12px; --label-2-weight: 500; --label-2-line-height: 16px; --label-2-transform: uppercase;
    --label-3-size: 12px; --label-3-weight: 300; --label-3-line-height: 16px; --label-3-style: italic;

    /* Classification */
    --classification-title-size: 12px; --classification-title-weight: 700; --classification-title-line-height: 16px;

    /* --------------------- EXTENDED COLORS --------------------- */
    /* Gray */ 
    --gray-01: #ffffff;
    --gray-02: #ebecee;
    --gray-03: #e2e4e9;
    --gray-04: #d4d8de;
    --gray-05: #c8cdd5;
    --gray-06: #a2a8b2;
    --gray-07: #7f858f;
    --gray-08: #6d737d;
    --gray-09: #505760;
    --gray-10: #383e46;
    --gray-11: #2a3038;
    --gray-12: #242a31;
    --gray-13: #1f252b;
    --gray-14: #0d1114;

    /* Pink */ 
    --pink-01: #f9e5f0;
    --pink-02: #fccade;
    --pink-03: #ffafcc;
    --pink-04: #ff759a;
    --pink-05: #ff5f8a;
    --pink-06: #f63966;
    --pink-07: #d63158;
    --pink-08: #bf2448;
    --pink-09: #961f3b;
    --pink-10: #720029;
    --pink-11: #4c0222;
    --pink-12: #27051b;

    /* Red */    
    --red-01: #f7e3e4;
    --red-02: #fbc7c9;
    --red-03: #ffabae;
    --red-04: #ed6c70;
    --red-05: #f74d52;
    --red-06: #e00007;
    --red-07: #ca0007;
    --red-08: #ab0005;
    --red-09: #8e0005;
    --red-10: #730004;
    --red-11: #4d0207;
    --red-12: #27050b;

    /* Orange */
    --orange-01: #f8e8d5;
    --orange-02: #fcd7ad;
    --orange-03: #ffc785;
    --orange-04: #ffb052;
    --orange-05: #ff9900;
    --orange-06: #f18302;
    --orange-07: #dd6907;
    --orange-08: #c9500c;
    --orange-09: #aa4206;
    --orange-10: #8b2a00;
    --orange-11: #621d00;
    --orange-12: #331200;

    /* Yellow */
    --yellow-01: #f5f0dc;
    --yellow-02: #faecb9;
    --yellow-03: #ffe896;
    --yellow-04: #ffdf6d;
    --yellow-05: #ffd43a;
    --yellow-06: #ffc600;
    --yellow-07: #e5b300;
    --yellow-08: #ca9800;
    --yellow-09: #af7e00;
    --yellow-10: #876100;
    --yellow-11: #563e02;
    --yellow-12: #251c04;

    /* Lime */
    --lime-01: #eef1d8;
    --lime-02: #edf0b8;
    --lime-03: #ecf098;
    --lime-04: #e1e76a;
    --lime-05: #c5d53e;
    --lime-06: #aac212;
    --lime-07: #91ae18;
    --lime-08: #7c9611;
    --lime-09: #667e0a;
    --lime-10: #486000;
    --lime-11: #2e3f07;
    --lime-12: #141e0e;

    /* Green */
    --green-01: #e8f2d5;
    --green-02: #e0f5b8;
    --green-03: #d9f89b;
    --green-04: #c3f068;
    --green-05: #8bd742;
    --green-06: #55be24;
    --green-07: #3da930;
    --green-08: #2e9422;
    --green-09: #1e7e13;
    --green-10: #0a5f00;
    --green-11: #07400c;
    --green-12: #042117;

    /* Teal */
    --teal-01: #e2f4f0;
    --teal-02: #ccf6ed;
    --teal-03: #b6f8e9;
    --teal-04: #9bf4e1;
    --teal-05: #51f1d9;
    --teal-06: #01d7c0;
    --teal-07: #00b0a6;
    --teal-08: #079993;
    --teal-09: #007971;
    --teal-10: #005448;
    --teal-11: #01322e;
    --teal-12: #031d20;

    /* Cyan */
    --cyan-01: #e0f1f6;
    --cyan-02: #caeefa;
    --cyan-03: #b4ecfe;
    --cyan-04: #7adfff;
    --cyan-05: #4bcaf2;
    --cyan-06: #1aaad8;
    --cyan-07: #0d90b9;
    --cyan-08: #0083ac;
    --cyan-09: #007296;
    --cyan-10: #005069;
    --cyan-11: #023445;
    --cyan-12: #041821;

    /* Blue */
    --blue-01: #e8effd;
    --blue-02: #d1e3fe;
    --blue-03: #bbd7ff;
    --blue-04: #78aeff;
    --blue-05: #4f96ff;
    --blue-06: #0064fa;
    --blue-07: #0056d7;
    --blue-08: #0031b0;
    --blue-09: #001d85;
    --blue-10: #00095a;
    --blue-11: #000b41;
    --blue-12: #000e28;

    /* Viloet */
    --violet-01: #e7e6f5;
    --violet-02: #d9d8f9;
    --violet-03: #cacaff;
    --violet-04: #a3a5ff;
    --violet-05: #8980ec;
    --violet-06: #685cd9;
    --violet-07: #5747c0;
    --violet-08: #4432b2;
    --violet-09: #2b2283;
    --violet-10: #211862;
    --violet-11: #1a1545;
    --violet-12: #040a1d;

    /* Purple */
    --purple-01: #eae1f4;
    --purple-02: #dfcef9;
    --purple-03: #d4bcff;
    --purple-04: #b58cff;
    --purple-05: #a460e9;
    --purple-06: #8a3ada;
    --purple-07: #7628c3;
    --purple-08: #610fb4;
    --purple-09: #3e007b;
    --purple-10: #2f005f;
    --purple-11: #240845;
    --purple-12: #04041c;

    /* Magenta */
    --magenta-01: #f1e3f2;
    --magenta-02: #edcceb;
    --magenta-03: #e9b5e6;
    --magenta-04: #da80cd;
    --magenta-05: #d160b9;
    --magenta-06: #c039a0;
    --magenta-07: #a62c8d;
    --magenta-08: #90197e;
    --magenta-09: #6a105b;
    --magenta-10: #500044;
    --magenta-11: #380533;
    --magenta-12: #16041c;

    /* Squid Ink */
    --squid-ink-01: #d9e6ff;
    --squid-ink-02: #a2b6cd;
    --squid-ink-03: #7f95af;
    --squid-ink-04: #617893;
    --squid-ink-05: #3a4a5e;
    --squid-ink-06: #263547;
    --squid-ink-07: #232f3e;
    --squid-ink-08: #1d2d41;
    --squid-ink-09: #13263d;
    --squid-ink-10: #0e2542;
    --squid-ink-11: #0b1f39;
    --squid-ink-12: #041935;
}

/* --------------------- APPLY TYPOGRAPHY STYLES --------------------- */

/* Headings */
h1 {
    font-size: var(--h1-size);
    font-weight: var(--h1-weight);
    line-height: var(--h1-line-height);
    font-family: var(--font-primary);
}

h2 {
    font-size: var(--h2-size);
    font-weight: var(--h2-weight);
    line-height: var(--h2-line-height);
    font-family: var(--font-primary);
}

h3 {
    font-size: var(--h3-size);
    font-weight: var(--h3-weight);
    line-height: var(--h3-line-height);
    font-family: var(--font-primary);
}

h4 {
    font-size: var(--h4-size);
    font-weight: var(--h4-weight);
    line-height: var(--h4-line-height);
    font-family: var(--font-primary);
}

h5 {
    font-size: var(--h5-size);
    font-weight: var(--h5-weight);
    line-height: var(--h5-line-height);
    font-family: var(--font-primary);
}

h6 {
    font-size: var(--h6-size);
    font-weight: var(--h6-weight);
    line-height: var(--h6-line-height);
    font-family: var(--font-primary);
}

h7 {
    font-size: var(--h7-size);
    font-weight: var(--h7-weight);
    line-height: var(--h7-line-height);
    font-family: var(--font-primary);
}

/* Title */
.title {
    font-size: var(--title-size);
    font-weight: var(--title-weight);
    line-height: var(--title-line-height);
    font-family: var(--font-primary);
}

/* Body Text */
p {
    font-size: var(--body-1-size);
    font-weight: var(--body-1-weight);
    line-height: var(--body-1-line-height);
    font-family: var(--font-primary);
}

p.body-2 {
    font-size: var(--body-2-size);
    font-weight: var(--body-2-weight);
    line-height: var(--body-2-line-height);
    font-style: var(--body-2-style);
    font-family: var(--font-primary);
}

p.underline {
    font-size: var(--body-underline-size);
    font-weight: var(--body-underline-weight);
    line-height: var(--body-underline-line-height);
    text-decoration: var(--body-underline-decoration);
    font-family: var(--font-primary);
}

p.strikethrough {
    font-size: var(--body-strikethrough-size);
    font-weight: var(--body-strikethrough-weight);
    line-height: var(--body-strikethrough-line-height);
    text-decoration: var(--body-strikethrough-decoration);
    font-family: var(--font-primary);
}

/* Labels */
.label-1 {
    font-size: var(--label-1-size);
    font-weight: var(--label-1-weight);
    line-height: var(--label-1-line-height);
    font-family: var(--font-primary);
}

.label-2 {
    font-size: var(--label-2-size);
    font-weight: var(--label-2-weight);
    line-height: var(--label-2-line-height);
    text-transform: var(--label-2-transform);
    font-family: var(--font-primary);
}

.label-3 {
    font-size: var(--label-3-size);
    font-weight: var(--label-3-weight);
    line-height: var(--label-3-line-height);
    font-style: var(--label-3-style);
    font-family: var(--font-primary);
}

/* Classification */
.classification-title {
    font-size: var(--classification-title-size);
    font-weight: var(--classification-title-weight);
    line-height: var(--classification-title-line-height);
    font-family: var(--font-primary);
}
