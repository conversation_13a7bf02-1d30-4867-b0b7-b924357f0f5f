import React from 'react';
import { View, ImageBackground, Text, StyleSheet } from 'react-native';
import { Svg, Path, Circle } from 'react-native-svg';

export default function Page2D() {
    return (
        <View style={styles.page2DContainer}>
            <View style={styles.windowbrowser}>
                <ImageBackground style={styles.screenshot20250224at125527PM1} source={{ uri: /* dummy image */ 'https://dummyimage.com/1920x121/000/fff.png' }}/>
            </View>
            {/* Visualwind:: can be replaced with <GlobalNavigationWorkbench  /> */}
            <View style={styles.globalNavigationWorkbench}>
                <View style={styles.actions}>
                    <View style={styles.icons}>
                        {/* Visualwind:: can be replaced with <BugReportDefault  /> */}
                        <View style={styles.bugReportDefault}>
                            <Svg style={styles.vector} width="12" height="14" viewBox="0 0 12 14" fill="none" >
<Path d="M11.25 4.00004H9.8925C9.555 3.41504 9.09 2.91254 8.5275 2.53004L9.225 1.83254C9.5175 1.54004 9.5175 1.06754 9.225 0.775039C8.9325 0.482539 8.46 0.482539 8.1675 0.775039L7.065 1.87754C6.645 1.78004 5.88 1.65254 4.9425 1.87754L3.8325 0.775039C3.54 0.482539 3.0675 0.482539 2.775 0.775039C2.4825 1.06754 2.4825 1.54004 2.775 1.83254L3.465 2.53004C2.91 2.91254 2.445 3.41504 2.1075 4.00004H0.75C0.3375 4.00004 0 4.33754 0 4.75004C0 5.16254 0.3375 5.50004 0.75 5.50004H1.5675C1.485 6.04754 1.5 6.15254 1.5 7.00004H0.75C0.3375 7.00004 0 7.33754 0 7.75004C0 8.16254 0.3375 8.50004 0.75 8.50004H1.5C1.5 9.35504 1.485 9.45254 1.5675 10H0.75C0.3375 10 0 10.3375 0 10.75C0 11.1625 0.3375 11.5 0.75 11.5H2.1075C2.8875 12.8425 4.335 13.75 6 13.75C7.665 13.75 9.1125 12.8425 9.8925 11.5H11.25C11.6625 11.5 12 11.1625 12 10.75C12 10.3375 11.6625 10 11.25 10H10.4325C10.515 9.45254 10.5 9.34754 10.5 8.50004H11.25C11.6625 8.50004 12 8.16254 12 7.75004C12 7.33754 11.6625 7.00004 11.25 7.00004H10.5C10.5 6.14504 10.515 6.04754 10.4325 5.50004H11.25C11.6625 5.50004 12 5.16254 12 4.75004C12 4.33754 11.6625 4.00004 11.25 4.00004ZM6.75 10H5.25C4.8375 10 4.5 9.66254 4.5 9.25004C4.5 8.83754 4.8375 8.50004 5.25 8.50004H6.75C7.1625 8.50004 7.5 8.83754 7.5 9.25004C7.5 9.66254 7.1625 10 6.75 10ZM6.75 7.00004H5.25C4.8375 7.00004 4.5 6.66254 4.5 6.25004C4.5 5.83754 4.8375 5.50004 5.25 5.50004H6.75C7.1625 5.50004 7.5 5.83754 7.5 6.25004C7.5 6.66254 7.1625 7.00004 6.75 7.00004Z" fill="white"/>
</Svg>

                        </View>
                    </View>
                    {/* Visualwind:: can be replaced with <Profile type={"profile"} style={"default"} image={"true"} /> */}
                    <View style={styles.profile}>
                        <ImageBackground style={styles.images51} source={{ uri: /* dummy image */ 'https://dummyimage.com/53x40/000/fff.png' }}/>
                        <ImageBackground style={styles.image2} source={{ uri: /* dummy image */ 'https://dummyimage.com/46x38/000/fff.png' }}/>
                    </View>
                </View>
                <View style={styles.nav}>
                    {/* Visualwind:: can be replaced with <_nav state={"default"} /> */}
                    <View style={styles._nav}>
                        <View style={styles.text}>
                            <Text style={styles.__nav}>
                                {`Home`}
                            </Text>
                        </View>
                    </View>
                    {/* Visualwind:: can be replaced with <___nav state={"selected"} /> */}
                    <View style={styles.___nav}>
                        <View style={styles._text}>
                            <Text style={styles.____nav}>
                                {`2D`}
                            </Text>
                        </View>
                        <View style={styles.rectangle1065}/>
                    </View>
                    {/* Visualwind:: can be replaced with <_____nav state={"default"} /> */}
                    <View style={styles._____nav}>
                        <View style={styles.__text}>
                            <Text style={styles.______nav}>
                                {`3D`}
                            </Text>
                        </View>
                    </View>
                    {/* Visualwind:: can be replaced with <_______nav state={"default"} /> */}
                    <View style={styles._______nav}>
                        <View style={styles.___text}>
                            <Text style={styles.________nav}>
                                {`Gallery`}
                            </Text>
                        </View>
                    </View>
                    {/* Visualwind:: can be replaced with <_________nav state={"default"} /> */}
                    <View style={styles._________nav}>
                        <View style={styles.____text}>
                            <Text style={styles.__________nav}>
                                {`About Us`}
                            </Text>
                        </View>
                    </View>
                </View>
                <Svg style={styles.group24} width="111" height="28" viewBox="0 0 111 28" fill="none" >
<Path d="M105.07 19.6001H103.618C103.366 19.6001 103.24 19.4741 103.24 19.2221V8.78925C103.24 8.53725 103.366 8.41125 103.618 8.41125H105.07C105.322 8.41125 105.448 8.53725 105.448 8.78925V12.5693C106.315 11.9544 107.222 11.6469 108.169 11.6469C108.885 11.6469 109.435 11.8435 109.818 12.2366C110.211 12.6197 110.407 13.169 110.407 13.8847V19.2221C110.407 19.4741 110.281 19.6001 110.029 19.6001H108.578C108.326 19.6001 108.2 19.4741 108.2 19.2221V14.5349C108.2 13.7385 107.832 13.3404 107.096 13.3404C106.542 13.3404 105.992 13.5117 105.448 13.8545V19.2221C105.448 19.4741 105.322 19.6001 105.07 19.6001Z" fill="white"/>
<Path d="M99.4958 19.7664C98.2761 19.7664 97.3387 19.4236 96.6835 18.7382C96.0283 18.0528 95.7007 17.0649 95.7007 15.7747C95.7007 14.4945 96.0434 13.4966 96.7288 12.7809C97.4143 12.0652 98.3668 11.7074 99.5865 11.7074C100.191 11.7074 100.761 11.8183 101.295 12.04C101.436 12.0904 101.527 12.1509 101.567 12.2215C101.608 12.282 101.628 12.3928 101.628 12.5541V13.2496C101.628 13.4815 101.542 13.5974 101.371 13.5974C101.34 13.5974 101.285 13.5873 101.204 13.5672C101.033 13.5168 100.836 13.4764 100.615 13.4462C100.403 13.416 100.196 13.4008 99.9948 13.4008C99.2892 13.4008 98.77 13.5823 98.4374 13.9452C98.1048 14.298 97.9384 14.8624 97.9384 15.6386V15.8503C97.9384 16.6164 98.0997 17.1808 98.4223 17.5437C98.7549 17.8965 99.264 18.0729 99.9494 18.0729C100.181 18.0729 100.383 18.0628 100.554 18.0427C100.736 18.0124 100.947 17.9671 101.189 17.9066C101.31 17.8764 101.391 17.8612 101.431 17.8612C101.592 17.8612 101.673 17.962 101.673 18.1636V18.9196C101.673 19.0809 101.653 19.1968 101.613 19.2674C101.572 19.3279 101.482 19.3833 101.34 19.4337C101.099 19.5244 100.831 19.6 100.539 19.6605C100.257 19.7311 99.9091 19.7664 99.4958 19.7664Z" fill="white"/>
<Path d="M88.6653 19.6001H87.2138C86.9618 19.6001 86.8358 19.4741 86.8358 19.2221V12.2518C86.8358 11.9998 86.9618 11.8738 87.2138 11.8738H88.3025C88.5343 11.8738 88.6754 11.9847 88.7258 12.2064L88.8619 12.7054C89.7691 11.9998 90.7368 11.647 91.7649 11.647C92.4806 11.647 93.03 11.8435 93.413 12.2367C93.8061 12.6197 94.0027 13.1691 94.0027 13.8847V19.2221C94.0027 19.4741 93.8767 19.6001 93.6247 19.6001H92.1732C91.9212 19.6001 91.7952 19.4741 91.7952 19.2221V14.5349C91.7952 13.7386 91.4273 13.3404 90.6914 13.3404C90.137 13.3404 89.5877 13.5118 89.0433 13.8545V19.2221C89.0433 19.4741 88.9173 19.6001 88.6653 19.6001Z" fill="white"/>
<Path d="M82.1938 19.8269C80.9136 19.8269 79.9359 19.4791 79.2605 18.7836C78.5952 18.0881 78.2626 17.07 78.2626 15.7294C78.2626 14.4391 78.5902 13.4362 79.2454 12.7205C79.9107 12.0048 80.838 11.647 82.0275 11.647C83.0254 11.647 83.7915 11.9242 84.3257 12.4786C84.87 13.0229 85.1422 13.8041 85.1422 14.8222C85.1422 15.054 85.1321 15.2607 85.1119 15.4421C85.1019 15.6235 85.0867 15.7747 85.0666 15.8957C85.0565 16.0267 85.0212 16.1225 84.9607 16.183C84.9103 16.2435 84.8146 16.2737 84.6735 16.2737H80.3038C80.3441 16.939 80.5457 17.4228 80.9086 17.7252C81.2815 18.0175 81.851 18.1637 82.6171 18.1637C83.1715 18.1637 83.8166 18.0679 84.5525 17.8764C84.6331 17.8563 84.6886 17.8462 84.7188 17.8462C84.8902 17.8462 84.9759 17.9621 84.9759 18.1939V18.8895C84.9759 19.0507 84.9557 19.1667 84.9154 19.2372C84.8751 19.2977 84.7843 19.3531 84.6432 19.4035C83.8771 19.6858 83.0607 19.8269 82.1938 19.8269ZM80.2887 14.9129H83.2522V14.6559C83.2522 13.668 82.8187 13.1741 81.9519 13.1741C81.4579 13.1741 81.0699 13.3253 80.7876 13.6277C80.5054 13.92 80.3391 14.3484 80.2887 14.9129Z" fill="white"/>
<Path d="M73.6927 19.7664C73.229 19.7664 72.7956 19.6857 72.3924 19.5245C71.9892 19.3632 71.6515 19.1364 71.3794 18.8441L71.2735 19.2674C71.2231 19.4892 71.082 19.6001 70.8502 19.6001H69.7313C69.4793 19.6001 69.3533 19.4741 69.3533 19.2221V8.78925C69.3533 8.53725 69.4793 8.41125 69.7313 8.41125H71.1828C71.4348 8.41125 71.5608 8.53725 71.5608 8.78925V12.4634C71.8531 12.2114 72.1908 12.0149 72.5738 11.8737C72.967 11.7225 73.3601 11.6469 73.7532 11.6469C74.731 11.6469 75.5021 12.0048 76.0665 12.7205C76.6411 13.4261 76.9284 14.3937 76.9284 15.6235C76.9284 16.44 76.7923 17.1607 76.5202 17.7857C76.248 18.4106 75.87 18.8995 75.3862 19.2523C74.9023 19.595 74.3378 19.7664 73.6927 19.7664ZM73.0425 18.1032C73.607 18.1032 74.0203 17.9117 74.2824 17.5286C74.5546 17.1456 74.6906 16.5408 74.6906 15.7142C74.6906 14.8776 74.5596 14.2677 74.2975 13.8847C74.0354 13.5017 73.6171 13.3101 73.0425 13.3101C72.7905 13.3101 72.5285 13.3505 72.2563 13.4311C71.9942 13.5017 71.7624 13.5974 71.5608 13.7184V17.6949C72.0043 17.9671 72.4982 18.1032 73.0425 18.1032Z" fill="white"/>
<Path d="M62.3674 19.6001H60.931C60.679 19.6001 60.553 19.4741 60.553 19.2221V8.78925C60.553 8.53725 60.679 8.41125 60.931 8.41125H62.3674C62.6194 8.41125 62.7454 8.53725 62.7454 8.78925V15.1699L65.2553 12.1459C65.3359 12.035 65.4166 11.9645 65.4972 11.9342C65.5779 11.8939 65.6887 11.8737 65.8299 11.8737H67.4779C67.6795 11.8737 67.7803 11.9493 67.7803 12.1005C67.7803 12.2114 67.7148 12.3374 67.5838 12.4785L64.8622 15.563L67.8559 18.9953C67.987 19.1364 68.0525 19.2573 68.0525 19.3581C68.0525 19.5194 67.9517 19.6001 67.7501 19.6001H66.0113C65.8702 19.6001 65.7593 19.5799 65.6787 19.5396C65.598 19.4993 65.5174 19.4287 65.4367 19.3279L62.7454 16.0771V19.2221C62.7454 19.4741 62.6194 19.6001 62.3674 19.6001Z" fill="white"/>
<Path d="M56.2548 19.6H54.8033C54.5513 19.6 54.4253 19.474 54.4253 19.222V12.2517C54.4253 11.9997 54.5513 11.8737 54.8033 11.8737H55.8919C56.1238 11.8737 56.2649 11.9846 56.3153 12.2063L56.5119 13.0228C56.8344 12.67 57.1771 12.3777 57.54 12.1459C57.9029 11.914 58.3212 11.7981 58.795 11.7981H59.0067C59.2687 11.7981 59.3998 11.9241 59.3998 12.1761V13.4462C59.3998 13.6982 59.2738 13.8242 59.0218 13.8242C58.9512 13.8242 58.8605 13.8191 58.7496 13.8091C58.6387 13.799 58.4976 13.7939 58.3263 13.7939C58.0944 13.7939 57.8122 13.8292 57.4795 13.8998C57.1469 13.9603 56.8647 14.0409 56.6328 14.1417V19.222C56.6328 19.474 56.5068 19.6 56.2548 19.6Z" fill="white"/>
<Path d="M48.8799 19.8269C47.7006 19.8269 46.7682 19.4741 46.0827 18.7685C45.3973 18.0528 45.0546 17.0398 45.0546 15.7294C45.0546 14.4291 45.3973 13.4261 46.0827 12.7205C46.7682 12.0048 47.7006 11.647 48.8799 11.647C50.0593 11.647 50.9917 12.0048 51.6771 12.7205C52.3626 13.4261 52.7053 14.4291 52.7053 15.7294C52.7053 17.0398 52.3626 18.0528 51.6771 18.7685C50.9917 19.4741 50.0593 19.8269 48.8799 19.8269ZM48.8799 18.1335C49.3839 18.1335 49.772 17.947 50.0442 17.574C50.3264 17.191 50.4675 16.5761 50.4675 15.7294C50.4675 14.8927 50.3264 14.2879 50.0442 13.915C49.772 13.5319 49.3839 13.3404 48.8799 13.3404C48.386 13.3404 47.9979 13.5319 47.7157 13.915C47.4334 14.2879 47.2923 14.8927 47.2923 15.7294C47.2923 16.5761 47.4334 17.191 47.7157 17.574C47.9979 17.947 48.386 18.1335 48.8799 18.1335Z" fill="white"/>
<Path d="M35.488 19.6001H33.8702C33.5879 19.6001 33.4115 19.4691 33.341 19.207L30.695 9.72675C30.6446 9.57555 30.6194 9.45459 30.6194 9.36387C30.6194 9.20259 30.7202 9.12195 30.9218 9.12195H32.6606C32.9327 9.12195 33.094 9.25299 33.1444 9.51507L34.732 17.1355L36.3045 9.77211C36.3549 9.51003 36.5313 9.37899 36.8337 9.37899H38.3457C38.6481 9.37899 38.8245 9.51003 38.8749 9.77211L40.4776 17.1053L42.0501 9.51507C42.1005 9.25299 42.2618 9.12195 42.5339 9.12195H44.1971C44.3987 9.12195 44.4995 9.20259 44.4995 9.36387C44.4995 9.45459 44.4743 9.57555 44.4239 9.72675L41.7779 19.207C41.7074 19.4691 41.531 19.6001 41.2487 19.6001H39.6309C39.3285 19.6001 39.1521 19.4691 39.1017 19.207L37.5292 12.2064L36.0172 19.207C35.9668 19.4691 35.7904 19.6001 35.488 19.6001Z" fill="white"/>
<Path d="M1.75791 6.07959C1.38425 6.29508 1.38492 6.8337 1.75912 7.04826L4.46404 8.59929C4.63677 8.69834 4.84921 8.69834 5.02194 8.59929L15.6064 2.53004C15.9806 2.31547 15.9813 1.77686 15.6076 1.56136L13.1603 0.149954C12.8136 -0.0499849 12.3864 -0.0499847 12.0397 0.149954L1.75791 6.07959Z" fill="white"/>
<Path d="M20.035 4.1147C19.8621 4.01497 19.6491 4.01471 19.4759 4.114L8.48155 10.4183C8.29418 10.5257 8.29418 10.7955 8.48155 10.903L12.1816 13.0246C12.4407 13.1732 12.7593 13.1732 13.0184 13.0246L23.4409 7.04826C23.8151 6.8337 23.8157 6.29508 23.4421 6.07959L20.035 4.1147Z" fill="white"/>
<Path d="M25.2 8.93923C25.2 8.50947 24.7344 8.24047 24.361 8.45454L13.8616 14.475C13.6008 14.6246 13.44 14.9019 13.44 15.2021V27.4402C13.44 27.8706 13.9068 28.1395 14.2802 27.9242L24.6403 21.9494C24.9867 21.7496 25.2 21.3806 25.2 20.9814V8.93923Z" fill="white"/>
<Path d="M10.9198 27.9242C11.2932 28.1395 11.76 27.8706 11.76 27.4402V15.2021C11.76 14.9019 11.5992 14.6246 11.3384 14.475L8.11947 12.6293C7.93281 12.5222 7.7 12.6567 7.7 12.8716L7.7 18.6296C7.7 18.8442 7.46773 18.9787 7.28107 18.8722L4.76214 17.4354C4.58767 17.3358 4.48 17.1506 4.48 16.95L4.48 10.8659C4.48 10.6657 4.37281 10.4809 4.19895 10.3812L0.838949 8.45454C0.465617 8.24047 0 8.50947 0 8.93923V20.9814C0 21.3806 0.213336 21.7496 0.55969 21.9494L10.9198 27.9242Z" fill="white"/>
</Svg>

            </View>
            <View style={styles.pageContent}>
                <View style={styles.content}>
                    <View style={styles.sessions}>
                        {/* Visualwind:: can be replaced with <Button style={"secondary"} size={"default"} behavior={"autowidth"} state={"default"} /> */}
                        <View style={styles.button}>
                            {/* Visualwind:: can be replaced with <Plus state={"default"} /> */}
                            <View style={styles.plus}>
                                <Svg style={styles.Vector} width="12" height="12" viewBox="0 0 12 12" fill="none" >
<Path d="M10.5 6.75H6.75V10.5C6.75 10.9125 6.4125 11.25 6 11.25C5.5875 11.25 5.25 10.9125 5.25 10.5V6.75L1.5 6.75C1.0875 6.75 0.75 6.4125 0.75 6C0.75 5.5875 1.0875 5.25 1.5 5.25L5.25 5.25L5.25 1.5C5.25 1.0875 5.5875 0.75 6 0.75C6.4125 0.75 6.75 1.0875 6.75 1.5L6.75 5.25H10.5C10.9125 5.25 11.25 5.5875 11.25 6C11.25 6.4125 10.9125 6.75 10.5 6.75Z" fill="#78AEFF"/>
</Svg>

                            </View>
                            <Text style={styles.label}>
                                {`Upload image`}
                            </Text>
                        </View>
                        <View style={styles.cards}>
                            {/* Visualwind:: can be replaced with <Sessioncard state={"default"} /> */}
                            <View style={styles.sessioncard}>
                                <ImageBackground style={styles.rectangle1532} source={{ uri: /* dummy image */ 'https://dummyimage.com/60x60/000/fff.png' }}/>
                                <View style={styles.info}>
                                    <View style={styles.titleDate}>
                                        <Text style={styles.title}>
                                            {`Title with a really long name`}
                                        </Text>
                                        <Text style={styles.mMDDYYYY}>
                                            {`MM/DD/YYYY`}
                                        </Text>
                                    </View>
                                </View>
                            </View>
                            {/* Visualwind:: can be replaced with <_sessioncard state={"selected"} /> */}
                            <View style={styles._sessioncard}>
                                <ImageBackground style={styles._rectangle1532} source={{ uri: /* dummy image */ 'https://dummyimage.com/60x60/000/fff.png' }}/>
                                <View style={styles._info}>
                                    <View style={styles._titleDate}>
                                        <Text style={styles._title}>
                                            {`Title`}
                                        </Text>
                                        <Text style={styles._mMDDYYYY}>
                                            {`MM/DD/YYYY`}
                                        </Text>
                                    </View>
                                </View>
                            </View>
                            {/* Visualwind:: can be replaced with <__sessioncard state={"default"} /> */}
                            <View style={styles.__sessioncard}>
                                <ImageBackground style={styles.__rectangle1532} source={{ uri: /* dummy image */ 'https://dummyimage.com/60x60/000/fff.png' }}/>
                                <View style={styles.__info}>
                                    <View style={styles.__titleDate}>
                                        <Text style={styles.__title}>
                                            {`Title`}
                                        </Text>
                                        <Text style={styles.__mMDDYYYY}>
                                            {`MM/DD/YYYY`}
                                        </Text>
                                    </View>
                                </View>
                            </View>
                        </View>
                    </View>
                    <View style={styles.container}>
                        <View style={styles.imageTools}>
                            <View style={styles.buttons}>
                                {/* Visualwind:: can be replaced with <IconButton state={"default"} size={"large"} /> */}
                                <View style={styles.iconButton}>
                                    {/* Visualwind:: can be replaced with <Nav_Crop state={"default"} /> */}
                                    <View style={styles.nav_Crop}>
                                        <Svg style={styles.Vector} width="16" height="16" viewBox="0 0 16 16" fill="none" >
<Path d="M16.005 12.7143C16.005 13.2666 15.5573 13.7143 15.005 13.7143L3.31193 13.7143C2.75964 13.7143 2.31193 13.2666 2.31193 12.7143L2.31193 1C2.31193 0.447715 2.75964 0 3.31193 0L3.59601 0C4.1483 0 4.59601 0.447715 4.59601 1L4.59601 10.9286C4.59601 11.2047 4.81987 11.4286 5.09601 11.4286L15.005 11.4286C15.5573 11.4286 16.005 11.8763 16.005 12.4286V12.7143ZM13.7095 3.32C13.7095 2.76772 13.2618 2.32 12.7095 2.32L5.16703 2.32L5.16703 4.60571L10.9254 4.60571C11.2016 4.60571 11.4254 4.82957 11.4254 5.10571L11.4254 10.8571H13.7095L13.7095 3.32ZM1.72949 2.32H1.005C0.45272 2.32 0.00500488 2.76772 0.00500488 3.32L0.00500488 3.60571C0.00500488 4.158 0.45272 4.60571 1.00501 4.60571H1.72949L1.72949 2.32ZM13.7095 14.2857H11.4254V15C11.4254 15.5523 11.8731 16 12.4254 16H12.7095C13.2618 16 13.7095 15.5523 13.7095 15V14.2857Z" fill="#C8CDD5"/>
</Svg>

                                    </View>
                                </View>
                                {/* Visualwind:: can be replaced with <_iconButton state={"default"} size={"large"} /> */}
                                <View style={styles._iconButton}>
                                    {/* Visualwind:: can be replaced with <Diff state={"default"} /> */}
                                    <View style={styles.diff}>
                                        <Svg style={styles.Vector} width="14" height="18" viewBox="0 0 14 18" fill="none" >
<Path fillRule="evenodd" clipRule="evenodd" d="M0 5C0 3.34315 1.34315 2 3 2H4C4.55228 2 5 2.44772 5 3C5 3.55228 4.55228 4 4 4L3 4C2.44772 4 2 4.44772 2 5L2 13C2 13.5523 2.44772 14 3 14H4C4.55228 14 5 14.4477 5 15C5 15.5523 4.55228 16 4 16H3C1.34315 16 0 14.6569 0 13L0 5Z" fill="#C8CDD5"/>
<Path d="M12 2H10C9.72386 2 9.5 2.22386 9.5 2.5L9.5 15.5C9.5 15.7761 9.72386 16 10 16H12C13.1046 16 14 15.1046 14 14L14 4C14 2.89543 13.1046 2 12 2Z" fill="#C8CDD5"/>
<Path d="M6 1C6 0.447716 6.44772 0 7 0C7.55228 0 8 0.447715 8 1L8 17C8 17.5523 7.55228 18 7 18C6.44772 18 6 17.5523 6 17L6 1Z" fill="#C8CDD5"/>
</Svg>

                                    </View>
                                </View>
                                {/* Visualwind:: can be replaced with <__iconButton state={"default"} size={"large"} /> */}
                                <View style={styles.__iconButton}>
                                    {/* Visualwind:: can be replaced with <Download state={"default"} /> */}
                                    <View style={styles.download}>
                                        <Svg style={styles.Vector} width="12" height="14" viewBox="0 0 12 14" fill="none" >
<Path d="M9.4425 5.25H8.25V1.5C8.25 1.0875 7.9125 0.75 7.5 0.75H4.5C4.0875 0.75 3.75 1.0875 3.75 1.5V5.25H2.5575C1.89 5.25 1.5525 6.06 2.025 6.5325L5.4675 9.975C5.76 10.2675 6.2325 10.2675 6.525 9.975L9.9675 6.5325C10.44 6.06 10.11 5.25 9.4425 5.25ZM0.75 12.75C0.75 13.1625 1.0875 13.5 1.5 13.5H10.5C10.9125 13.5 11.25 13.1625 11.25 12.75C11.25 12.3375 10.9125 12 10.5 12H1.5C1.0875 12 0.75 12.3375 0.75 12.75Z" fill="#C8CDD5"/>
</Svg>

                                    </View>
                                </View>
                            </View>
                            <View style={styles.generatedimageVideo}>
                                <ImageBackground style={styles.rectangle1624} source={{ uri: /* dummy image */ 'https://dummyimage.com/656x655/000/fff.png' }}/>
                            </View>
                            <View style={styles._buttons}>
                                {/* Visualwind:: can be replaced with <ToggleGroupVertical size={"large"} /> */}
                                <View style={styles.toggleGroupVertical}>
                                    {/* Visualwind:: can be replaced with <ButtonToggleIconSlotVertical state={"default"} position={"top"} size={"large"} /> */}
                                    <View style={styles.buttonToggleIconSlotVertical}>
                                        {/* Visualwind:: can be replaced with <Magic_Wand state={"default"} /> */}
                                        <View style={styles.magic_Wand}>
                                            <Svg style={styles.Vector} width="19" height="19" viewBox="0 0 19 19" fill="none" >
<Path d="M9.84434 6.77856L12.5602 9.49443L3.76176 18.2929C3.37124 18.6834 2.73807 18.6834 2.34755 18.2929L1.04588 16.9912C0.65536 16.6007 0.65536 15.9675 1.04588 15.577L9.84434 6.77856Z" fill="#C8CDD5"/>
<Path fillRule="evenodd" clipRule="evenodd" d="M15.2735 5.36714L13.9718 4.06548L11.9375 6.09975L13.2392 7.40141L15.2735 5.36714ZM14.6789 3.35837C14.2884 2.96784 13.6552 2.96784 13.2647 3.35837L10.5233 6.09975L13.2392 8.81562L15.9806 6.07424C16.3711 5.68372 16.3711 5.05056 15.9806 4.66003L14.6789 3.35837Z" fill="#C8CDD5"/>
<Path d="M9.00002 2L9.63641 3.3636L11 4L9.63641 4.6364L9.00002 6L8.36362 4.6364L7.00002 4L8.36362 3.3636L9.00002 2Z" fill="#C8CDD5"/>
<Path d="M17 0L17.6364 1.3636L19 2L17.6364 2.6364L17 4L16.3636 2.6364L15 2L16.3636 1.3636L17 0Z" fill="#C8CDD5"/>
<Path d="M16 8L16.9546 10.0454L19 11L16.9546 11.9546L16 14L15.0454 11.9546L13 11L15.0454 10.0454L16 8Z" fill="#C8CDD5"/>
</Svg>

                                        </View>
                                    </View>
                                    {/* Visualwind:: can be replaced with <DivH  /> */}
                                    <View style={styles.divH}>
                                        <View style={styles.rectangle1064}/>
                                    </View>
                                    {/* Visualwind:: can be replaced with <_buttonToggleIconSlotVertical state={"selected"} position={"bottom"} size={"large"} /> */}
                                    <View style={styles._buttonToggleIconSlotVertical}>
                                        {/* Visualwind:: can be replaced with <Paint state={"default"} /> */}
                                        <View style={styles.paint}>
                                            <Svg style={styles.Vector} width="19" height="18" viewBox="0 0 19 18" fill="none" >
<Path d="M3.99999 18.0002C3.24999 18.0002 2.50833 17.8169 1.77499 17.4502C1.36664 17.246 1.00222 17.0057 0.681719 16.7292C0.369763 16.46 0.591937 15.9324 0.952306 15.7327C1.07589 15.6642 1.20012 15.5825 1.32499 15.4877C1.77499 15.146 1.99999 14.6502 1.99999 14.0002C1.99999 13.1669 2.29166 12.4585 2.87499 11.8752C3.45833 11.2919 4.16666 11.0002 4.99999 11.0002C5.83333 11.0002 6.54166 11.2919 7.12499 11.8752C7.70833 12.4585 7.99999 13.1669 7.99999 14.0002C7.99999 15.1002 7.60833 16.0419 6.82499 16.8252C6.04166 17.6085 5.09999 18.0002 3.99999 18.0002ZM10.4571 11.2931C10.0666 11.6836 9.43341 11.6836 9.04289 11.2931L7.7071 9.95731C7.31658 9.56679 7.31658 8.93362 7.7071 8.5431L15.95 0.300207C16.1333 0.116874 16.3625 0.0210404 16.6375 0.012707C16.9125 0.0043737 17.15 0.100207 17.35 0.300207L18.7 1.65021C18.9 1.85021 19 2.08354 19 2.35021C19 2.61687 18.9 2.85021 18.7 3.05021L10.4571 11.2931Z" fill="#0D1114"/>
</Svg>

                                        </View>
                                    </View>
                                </View>
                                <View style={styles.slider}>
                                    <View style={styles.size}>
                                        <Svg style={styles.ellipse2} width="17" height="16" viewBox="0 0 17 16" fill="none" >
<Circle cx="8.5" cy="8" r="8" fill="#C8CDD5"/>
</Svg>

                                    </View>
                                    <View style={styles.frame1419}>
                                        <Svg style={styles.line1stroke} width="5" height="160" viewBox="0 0 5 160" fill="none" >
<Path fillRule="evenodd" clipRule="evenodd" d="M2.5 160C1.39543 160 0.5 159.105 0.5 158L0.500007 2C0.500007 0.895429 1.39544 -1.35705e-07 2.5 -8.74226e-08C3.60457 -3.91405e-08 4.5 0.895431 4.5 2L4.49999 158C4.49999 159.105 3.60456 160 2.5 160Z" fill="#6D737D"/>
</Svg>

                                        <View style={styles.rectangle16}/>
                                    </View>
                                    <View style={styles._size}>
                                        <Svg style={styles.Ellipse2} width="6" height="6" viewBox="0 0 6 6" fill="none" >
<Circle cx="3" cy="3.00098" r="3" fill="#C8CDD5"/>
</Svg>

                                    </View>
                                </View>
                            </View>
                        </View>
                        <View style={styles.controls}>
                            <View style={styles.frame1410}>
                                {/* Visualwind:: can be replaced with <TextField state={"filled"} /> */}
                                <View style={styles.textField}>
                                    <View style={styles._label}>
                                        <Text style={styles.__label}>
                                            {`Prompt`}
                                        </Text>
                                    </View>
                                    <View style={styles.field}>
                                        <View style={styles.input}>
                                            <Text style={styles._____text}>
                                                {`melanistic fox laying down`}
                                            </Text>
                                        </View>
                                    </View>
                                </View>
                                <View style={styles.frame1426}>
                                    <Text style={styles.___label}>
                                        {`Style image`}
                                    </Text>
                                    {/* Visualwind:: can be replaced with <___iconButton state={"default"} size={"large"} /> */}
                                    <View style={styles.___iconButton}>
                                        {/* Visualwind:: can be replaced with <Image_Add state={"default"} /> */}
                                        <View style={styles.image_Add}>
                                            <Svg style={styles.Vector} width="15" height="15" viewBox="0 0 15 15" fill="none" >
<Path d="M11 5V4H10C9.44771 4 9 3.55228 9 3C9 2.44772 9.44771 2 10 2H11V1C11 0.447715 11.4477 0 12 0C12.5523 0 13 0.447715 13 1V2H14C14.5523 2 15 2.44772 15 3C15 3.55228 14.5523 4 14 4H13V5C13 5.55228 12.5523 6 12 6C11.4477 6 11 5.55228 11 5Z" fill="#C8CDD5"/>
<Path fillRule="evenodd" clipRule="evenodd" d="M1.75 1.25H8.40212C8.14453 1.7786 8 2.37242 8 3C8 5.20914 9.79086 7 12 7C12.6276 7 13.2214 6.85547 13.75 6.59788V13.25C13.75 14.075 13.075 14.75 12.25 14.75H1.75C0.925 14.75 0.25 14.075 0.25 13.25V2.75C0.25 1.925 0.925 1.25 1.75 1.25ZM4.675 9.485L6.25 11.3825L8.575 8.39C8.725 8.195 9.025 8.195 9.175 8.3975L11.8075 11.9075C11.995 12.155 11.815 12.5075 11.5075 12.5075H2.515C2.2 12.5075 2.0275 12.1475 2.2225 11.9L4.09 9.5C4.2325 9.305 4.5175 9.2975 4.675 9.485Z" fill="#C8CDD5"/>
</Svg>

                                        </View>
                                    </View>
                                </View>
                                <View style={styles.frame1411}>
                                    {/* Visualwind:: can be replaced with <FormField state={"filled"} size={"default"} /> */}
                                    <View style={styles.formField}>
                                        <View style={styles._field}>
                                            <View style={styles._input}>
                                                <Text style={styles.______text}>
                                                    {`420`}
                                                </Text>
                                            </View>
                                            {/* Visualwind:: can be replaced with <Randomize state={"default"} /> */}
                                            <View style={styles.randomize}>
                                                <Svg style={styles.Vector} width="16" height="16" viewBox="0 0 16 16" fill="none" >
<Path d="M4 13.3333C4.37037 13.3333 4.68519 13.2037 4.94444 12.9444C5.2037 12.6852 5.33333 12.3704 5.33333 12C5.33333 11.6296 5.2037 11.3148 4.94444 11.0556C4.68519 10.7963 4.37037 10.6667 4 10.6667C3.62963 10.6667 3.31481 10.7963 3.05556 11.0556C2.7963 11.3148 2.66667 11.6296 2.66667 12C2.66667 12.3704 2.7963 12.6852 3.05556 12.9444C3.31481 13.2037 3.62963 13.3333 4 13.3333ZM4 5.33333C4.37037 5.33333 4.68519 5.2037 4.94444 4.94444C5.2037 4.68519 5.33333 4.37037 5.33333 4C5.33333 3.62963 5.2037 3.31481 4.94444 3.05556C4.68519 2.7963 4.37037 2.66667 4 2.66667C3.62963 2.66667 3.31481 2.7963 3.05556 3.05556C2.7963 3.31481 2.66667 3.62963 2.66667 4C2.66667 4.37037 2.7963 4.68519 3.05556 4.94444C3.31481 5.2037 3.62963 5.33333 4 5.33333ZM8 9.33333C8.37037 9.33333 8.68519 9.2037 8.94444 8.94444C9.2037 8.68519 9.33333 8.37037 9.33333 8C9.33333 7.62963 9.2037 7.31482 8.94444 7.05556C8.68519 6.7963 8.37037 6.66667 8 6.66667C7.62963 6.66667 7.31482 6.7963 7.05556 7.05556C6.7963 7.31482 6.66667 7.62963 6.66667 8C6.66667 8.37037 6.7963 8.68519 7.05556 8.94444C7.31482 9.2037 7.62963 9.33333 8 9.33333ZM12 13.3333C12.3704 13.3333 12.6852 13.2037 12.9444 12.9444C13.2037 12.6852 13.3333 12.3704 13.3333 12C13.3333 11.6296 13.2037 11.3148 12.9444 11.0556C12.6852 10.7963 12.3704 10.6667 12 10.6667C11.6296 10.6667 11.3148 10.7963 11.0556 11.0556C10.7963 11.3148 10.6667 11.6296 10.6667 12C10.6667 12.3704 10.7963 12.6852 11.0556 12.9444C11.3148 13.2037 11.6296 13.3333 12 13.3333ZM12 5.33333C12.3704 5.33333 12.6852 5.2037 12.9444 4.94444C13.2037 4.68519 13.3333 4.37037 13.3333 4C13.3333 3.62963 13.2037 3.31481 12.9444 3.05556C12.6852 2.7963 12.3704 2.66667 12 2.66667C11.6296 2.66667 11.3148 2.7963 11.0556 3.05556C10.7963 3.31481 10.6667 3.62963 10.6667 4C10.6667 4.37037 10.7963 4.68519 11.0556 4.94444C11.3148 5.2037 11.6296 5.33333 12 5.33333ZM1.77778 16C1.28889 16 0.87037 15.8259 0.522222 15.4778C0.174074 15.1296 0 14.7111 0 14.2222V1.77778C0 1.28889 0.174074 0.87037 0.522222 0.522222C0.87037 0.174074 1.28889 0 1.77778 0H14.2222C14.7111 0 15.1296 0.174074 15.4778 0.522222C15.8259 0.87037 16 1.28889 16 1.77778V14.2222C16 14.7111 15.8259 15.1296 15.4778 15.4778C15.1296 15.8259 14.7111 16 14.2222 16H1.77778ZM1.77778 14.2222H14.2222V1.77778H1.77778V14.2222Z" fill="#C8CDD5"/>
</Svg>

                                            </View>
                                        </View>
                                    </View>
                                </View>
                                <View style={styles.__buttons}>
                                    {/* Visualwind:: can be replaced with <_button style={"amazonPrimary"} size={"default"} behavior={"autowidth"} state={"default"} /> */}
                                    <View style={styles._button}>
                                        {/* Visualwind:: can be replaced with <Nav_GenAI state={"default"} /> */}
                                        <View style={styles.nav_GenAI}>
                                            <Svg style={styles.Vector} width="17" height="16" viewBox="0 0 17 16" fill="none" >
<Path fillRule="evenodd" clipRule="evenodd" d="M9.43026 0.633109C9.27959 0.251077 8.91067 0 8.5 0C8.08933 0 7.72041 0.251077 7.56974 0.633109L5.74918 5.24918L1.13311 7.06974C0.751077 7.22041 0.5 7.58933 0.5 8C0.5 8.41067 0.751077 8.77959 1.13311 8.93026L5.74918 10.7508L7.56974 15.3669C7.72041 15.7489 8.08933 16 8.5 16C8.91067 16 9.27959 15.7489 9.43026 15.3669L11.2508 10.7508L15.8669 8.93026C16.2489 8.77959 16.5 8.41067 16.5 8C16.5 7.58933 16.2489 7.22041 15.8669 7.06974L11.2508 5.24918L9.43026 0.633109ZM7.45037 6.38699L8.5 3.72561L9.54963 6.38699C9.65128 6.64471 9.85529 6.84872 10.113 6.95037L12.7744 8L10.113 9.04964C9.85529 9.15128 9.65128 9.35529 9.54964 9.61301L8.5 12.2744L7.45037 9.61301C7.34872 9.35529 7.14471 9.15128 6.88699 9.04963L4.22561 8L6.88699 6.95037C7.14471 6.84872 7.34872 6.64471 7.45037 6.38699Z" fill="#0D1114"/>
<Path d="M3.51302 1.5584L2.78571 0L2.0584 1.5584L0.5 2.28571L2.0584 3.01302L2.78571 4.57143L3.51302 3.01302L5.07143 2.28571L3.51302 1.5584Z" fill="#0D1114"/>
</Svg>

                                        </View>
                                        <Text style={styles.____label}>
                                            {`Restyle`}
                                        </Text>
                                    </View>
                                    {/* Visualwind:: can be replaced with <__button style={"amazonPrimary"} size={"default"} behavior={"autowidth"} state={"default"} /> */}
                                    <View style={styles.__button}>
                                        {/* Visualwind:: can be replaced with <Video_AI state={"default"} /> */}
                                        <View style={styles.video_AI}>
                                            <Svg style={styles.Vector} width="16" height="16" viewBox="0 0 16 16" fill="none" >
<Path d="M2 0L2.6364 1.3636L4 2L2.6364 2.6364L2 4L1.3636 2.6364L0 2L1.3636 1.3636L2 0Z" fill="#0D1114"/>
<Path d="M4 12.885L4 5.11501C4 4.52251 4.6525 4.16251 5.155 4.48501L11.26 8.36251C11.725 8.66251 11.725 9.33751 11.26 9.63001L5.155 13.515C4.6525 13.8375 4 13.4775 4 12.885Z" fill="#0D1114"/>
<Path d="M13.6137 2.38631L12.5 0L11.3863 2.38631L9 3.5L11.3863 4.61369L12.5 7L13.6137 4.61369L16 3.5L13.6137 2.38631Z" fill="#0D1114"/>
<Path d="M10 12L10.6364 13.3636L12 14L10.6364 14.6364L10 16L9.3636 14.6364L8 14L9.3636 13.3636L10 12Z" fill="#0D1114"/>
</Svg>

                                        </View>
                                        <Text style={styles._____label}>
                                            {`Image to video`}
                                        </Text>
                                    </View>
                                </View>
                            </View>
                            <View style={styles.advanced}>
                                <View style={styles._advanced}>
                                    <Text style={styles.__advanced}>
                                        {`Advanced`}
                                    </Text>
                                    {/* Visualwind:: can be replaced with <Directional_Filled_Large direction={"down"} state={"default"} /> */}
                                    <View style={styles.directional_Filled_Large}>
                                        <Svg style={styles.Vector} width="10" height="8" viewBox="0 0 10 8" fill="none" >
<Path d="M8.88103 0.195557L1.11103 0.195557C0.518527 0.195557 0.158526 0.848057 0.481027 1.35056L4.36603 7.45556C4.65853 7.92056 5.33353 7.92056 5.63353 7.45556L9.51103 1.35056C9.83353 0.848057 9.47353 0.195557 8.88103 0.195557Z" fill="#C8CDD5"/>
</Svg>

                                    </View>
                                    {/* Visualwind:: can be replaced with <_divH  /> */}
                                    <View style={styles._divH}>
                                        <View style={styles._rectangle1064}/>
                                    </View>
                                </View>
                            </View>
                        </View>
                    </View>
                    <View style={styles.gallery}>
                        <View style={styles.___title}>
                            <Text style={styles.restyledmedia}>
                                {`Restyled media`}
                            </Text>
                        </View>
                        {/* Visualwind:: can be replaced with <SecondaryTabsGroup  /> */}
                        <View style={styles.secondaryTabsGroup}>
                            <View style={styles.tabs}>
                                {/* Visualwind:: can be replaced with <SecondaryTabs state={"active"} /> */}
                                <View style={styles.secondaryTabs}>
                                    <Text style={styles.______label}>
                                        {`Images`}
                                    </Text>
                                    <View style={styles.underline}/>
                                </View>
                                {/* Visualwind:: can be replaced with <_secondaryTabs state={"default"} /> */}
                                <View style={styles._secondaryTabs}>
                                    <Text style={styles._______label}>
                                        {`Video`}
                                    </Text>
                                </View>
                                {/* Visualwind:: can be replaced with <__secondaryTabs state={"default"} /> */}
                                <View style={styles.__secondaryTabs}>
                                    <Text style={styles.________label}>
                                        {`All`}
                                    </Text>
                                </View>
                            </View>
                        </View>
                        <View style={styles._container}>
                            {/* Visualwind:: can be replaced with <ImageCard03 state={"default"} /> */}
                            <View style={styles.imageCard03}>
                                <View style={styles.defaultImage}>
                                    <ImageBackground style={styles.___rectangle1532} source={{ uri: /* dummy image */ 'https://dummyimage.com/60x60/000/fff.png' }}/>
                                </View>
                            </View>
                            {/* Visualwind:: can be replaced with <_imageCard03 state={"selected"} /> */}
                            <View style={styles._imageCard03}>
                                <View style={styles._defaultImage}>
                                    <ImageBackground style={styles.____rectangle1532} source={{ uri: /* dummy image */ 'https://dummyimage.com/60x60/000/fff.png' }}/>
                                </View>
                            </View>
                            {/* Visualwind:: can be replaced with <__imageCard03 state={"default"} /> */}
                            <View style={styles.__imageCard03}>
                                <View style={styles.__defaultImage}>
                                    <ImageBackground style={styles._____rectangle1532} source={{ uri: /* dummy image */ 'https://dummyimage.com/60x60/000/fff.png' }}/>
                                </View>
                            </View>
                            {/* Visualwind:: can be replaced with <___imageCard03 state={"default"} /> */}
                            <View style={styles.___imageCard03}>
                                <View style={styles.___defaultImage}>
                                    <ImageBackground style={styles.______rectangle1532} source={{ uri: /* dummy image */ 'https://dummyimage.com/60x60/000/fff.png' }}/>
                                </View>
                            </View>
                        </View>
                    </View>
                </View>
            </View>
        </View>  )
}

const styles = StyleSheet.create({
    page2DContainer: {
        position: "relative",
        flexShrink: 0,
        height: 1080,
        width: 1920,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0,
        borderRadius: 10
    },
    windowbrowser: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        height: 121,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0
    },
    screenshot20250224at125527PM1: {
        position: "absolute",
        flexShrink: 0,
        width: 1920,
        height: 121
    },
    globalNavigationWorkbench: {
        position: "relative",
        flexShrink: 0,
        height: 44,
        width: 1920,
        backgroundColor: "rgba(35, 47, 62, 1)",
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0
    },
    actions: {
        position: "absolute",
        flexShrink: 0,
        top: 6,
        bottom: 6,
        right: 8,
        display: "flex",
        alignItems: "center",
        columnGap: 4
    },
    icons: {
        position: "relative",
        flexShrink: 0,
        display: "flex",
        alignItems: "center",
        justifyContent: "flex-end",
        columnGap: 16
    },
    bugReportDefault: {
        position: "relative",
        flexShrink: 0,
        height: 24,
        width: 24,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0,
        borderRadius: 4
    },
    vector: {
        position: "absolute",
        flexShrink: 0,
        top: 6,
        right: 6,
        bottom: 5,
        left: 6,
        overflow: "visible"
    },
    profile: {
        position: "relative",
        flexShrink: 0,
        height: 32,
        width: 32,
        transform: "rotateZ(-90.00deg)",
        borderStyle: "solid",
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0,
        borderWidth: 1,
        borderColor: "rgba(127, 133, 143, 1)",
        borderRadius: 50
    },
    images51: {
        position: "absolute",
        flexShrink: 0,
        top: -4,
        left: -11,
        width: 53,
        height: 40,
        transform: "rotateZ(90.00deg)"
    },
    image2: {
        position: "absolute",
        flexShrink: 0,
        top: -3,
        right: -4,
        bottom: -3,
        left: -10,
        transform: "rotateZ(90.00deg)"
    },
    nav: {
        position: "absolute",
        flexShrink: 0,
        top: 4,
        left: 787,
        right: 788,
        display: "flex",
        alignItems: "flex-start",
        columnGap: 20
    },
    _nav: {
        position: "relative",
        flexShrink: 0,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 7,
        paddingHorizontal: 2,
        paddingVertical: 0
    },
    text: {
        position: "relative",
        flexShrink: 0,
        display: "flex",
        alignItems: "flex-start",
        columnGap: 0,
        padding: 4
    },
    __nav: {
        position: "relative",
        flexShrink: 0,
        textAlign: "left",
        color: "rgba(200, 205, 213, 1)",
        fontFamily: "Amazon Ember",
        fontSize: 16,
        fontWeight: 400,
        lineHeight: "24px"
    },
    ___nav: {
        position: "relative",
        flexShrink: 0,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 4,
        paddingHorizontal: 2,
        paddingVertical: 0
    },
    _text: {
        position: "relative",
        flexShrink: 0,
        display: "flex",
        alignItems: "flex-start",
        columnGap: 0,
        padding: 4
    },
    ____nav: {
        position: "relative",
        flexShrink: 0,
        textAlign: "left",
        color: "rgba(120, 174, 255, 1)",
        fontFamily: "Amazon Ember",
        fontSize: 16,
        fontWeight: 400,
        lineHeight: "24px"
    },
    rectangle1065: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        height: 4,
        borderTopLeftRadius: 4,
        borderTopRightRadius: 4,
        borderBottomRightRadius: 0,
        borderBottomLeftRadius: 0,
        backgroundColor: "rgba(120, 174, 255, 1)"
    },
    _____nav: {
        position: "relative",
        flexShrink: 0,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 7,
        paddingHorizontal: 2,
        paddingVertical: 0
    },
    __text: {
        position: "relative",
        flexShrink: 0,
        display: "flex",
        alignItems: "flex-start",
        columnGap: 0,
        padding: 4
    },
    ______nav: {
        position: "relative",
        flexShrink: 0,
        textAlign: "left",
        color: "rgba(200, 205, 213, 1)",
        fontFamily: "Amazon Ember",
        fontSize: 16,
        fontWeight: 400,
        lineHeight: "24px"
    },
    _______nav: {
        position: "relative",
        flexShrink: 0,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 7,
        paddingHorizontal: 2,
        paddingVertical: 0
    },
    ___text: {
        position: "relative",
        flexShrink: 0,
        display: "flex",
        alignItems: "flex-start",
        columnGap: 0,
        padding: 4
    },
    ________nav: {
        position: "relative",
        flexShrink: 0,
        textAlign: "left",
        color: "rgba(200, 205, 213, 1)",
        fontFamily: "Amazon Ember",
        fontSize: 16,
        fontWeight: 400,
        lineHeight: "24px"
    },
    _________nav: {
        position: "relative",
        flexShrink: 0,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 7,
        paddingHorizontal: 2,
        paddingVertical: 0
    },
    ____text: {
        position: "relative",
        flexShrink: 0,
        display: "flex",
        alignItems: "flex-start",
        columnGap: 0,
        padding: 4
    },
    __________nav: {
        position: "relative",
        flexShrink: 0,
        textAlign: "left",
        color: "rgba(200, 205, 213, 1)",
        fontFamily: "Amazon Ember",
        fontSize: 16,
        fontWeight: 400,
        lineHeight: "24px"
    },
    group24: {
        position: "absolute",
        flexShrink: 0,
        top: 8,
        height: 28,
        left: 8,
        width: 110
    },
    pageContent: {
        position: "relative",
        alignSelf: "stretch",
        flexGrow: 1,
        flexShrink: 1,
        flexBasis: 0,
        paddingTop: 32,
        paddingBottom: 20,
        backgroundColor: "rgba(31, 37, 43, 1)",
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0,
        paddingHorizontal: 20
    },
    content: {
        position: "relative",
        alignSelf: "stretch",
        flexGrow: 1,
        flexShrink: 1,
        flexBasis: 0,
        display: "flex",
        alignItems: "flex-start",
        justifyContent: "center",
        columnGap: 40
    },
    sessions: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        width: 300,
        backgroundColor: "rgba(42, 48, 56, 1)",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        rowGap: 16,
        padding: 12,
        borderRadius: 4
    },
    button: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        height: 40,
        borderStyle: "solid",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        columnGap: 4,
        paddingHorizontal: 16,
        paddingVertical: 10,
        borderWidth: 1,
        borderColor: "rgba(120, 174, 255, 1)",
        borderRadius: 4
    },
    plus: {
        position: "relative",
        flexShrink: 0,
        height: 24,
        width: 24,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0,
        borderRadius: 4
    },
    _vector: {
        position: "absolute",
        flexShrink: 0,
        top: 7,
        right: 7,
        bottom: 7,
        left: 7,
        overflow: "visible"
    },
    label: {
        position: "relative",
        flexShrink: 0,
        textAlign: "center",
        color: "rgba(120, 174, 255, 1)",
        fontFamily: "Open Sans",
        fontSize: 14,
        fontWeight: 600,
        lineHeight: "20px"
    },
    cards: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 5
    },
    sessioncard: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        backgroundColor: "rgba(31, 37, 43, 1)",
        display: "flex",
        alignItems: "flex-start",
        columnGap: 4,
        padding: 8,
        borderRadius: 8
    },
    rectangle1532: {
        position: "relative",
        flexShrink: 0,
        width: 60,
        height: 60,
        borderRadius: 4
    },
    info: {
        position: "relative",
        flexGrow: 1,
        flexShrink: 1,
        flexBasis: 0,
        display: "flex",
        alignItems: "flex-start",
        columnGap: 4
    },
    titleDate: {
        position: "relative",
        flexGrow: 1,
        flexShrink: 1,
        flexBasis: 0,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0
    },
    title: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        textAlign: "left",
        color: "rgba(255, 255, 255, 1)",
        fontFamily: "Amazon Ember",
        fontSize: 16,
        fontWeight: 700,
        lineHeight: "24px"
    },
    mMDDYYYY: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        textAlign: "left",
        color: "rgba(200, 205, 213, 1)",
        fontFamily: "Amazon Ember",
        fontSize: 14,
        fontWeight: 300,
        lineHeight: "20px"
    },
    _sessioncard: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        borderStyle: "solid",
        backgroundColor: "rgba(0, 14, 40, 1)",
        display: "flex",
        alignItems: "flex-start",
        columnGap: 4,
        padding: 8,
        borderWidth: 1,
        borderColor: "rgba(120, 174, 255, 1)",
        borderRadius: 8
    },
    _rectangle1532: {
        position: "relative",
        flexShrink: 0,
        width: 60,
        height: 60,
        borderRadius: 4
    },
    _info: {
        position: "relative",
        flexGrow: 1,
        flexShrink: 1,
        flexBasis: 0,
        display: "flex",
        alignItems: "flex-start",
        columnGap: 4
    },
    _titleDate: {
        position: "relative",
        flexGrow: 1,
        flexShrink: 1,
        flexBasis: 0,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0
    },
    _title: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        textAlign: "left",
        color: "rgba(255, 255, 255, 1)",
        fontFamily: "Amazon Ember",
        fontSize: 16,
        fontWeight: 700,
        lineHeight: "24px"
    },
    _mMDDYYYY: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        textAlign: "left",
        color: "rgba(200, 205, 213, 1)",
        fontFamily: "Amazon Ember",
        fontSize: 14,
        fontWeight: 300,
        lineHeight: "20px"
    },
    __sessioncard: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        backgroundColor: "rgba(31, 37, 43, 1)",
        display: "flex",
        alignItems: "flex-start",
        columnGap: 4,
        padding: 8,
        borderRadius: 8
    },
    __rectangle1532: {
        position: "relative",
        flexShrink: 0,
        width: 60,
        height: 60,
        borderRadius: 4
    },
    __info: {
        position: "relative",
        flexGrow: 1,
        flexShrink: 1,
        flexBasis: 0,
        display: "flex",
        alignItems: "flex-start",
        columnGap: 4
    },
    __titleDate: {
        position: "relative",
        flexGrow: 1,
        flexShrink: 1,
        flexBasis: 0,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0
    },
    __title: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        textAlign: "left",
        color: "rgba(255, 255, 255, 1)",
        fontFamily: "Amazon Ember",
        fontSize: 16,
        fontWeight: 700,
        lineHeight: "24px"
    },
    __mMDDYYYY: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        textAlign: "left",
        color: "rgba(200, 205, 213, 1)",
        fontFamily: "Amazon Ember",
        fontSize: 14,
        fontWeight: 300,
        lineHeight: "20px"
    },
    container: {
        position: "relative",
        alignSelf: "stretch",
        flexGrow: 1,
        flexShrink: 1,
        flexBasis: 0,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        rowGap: 8
    },
    imageTools: {
        position: "relative",
        flexShrink: 0,
        height: 655,
        display: "flex",
        alignItems: "flex-start",
        justifyContent: "center",
        columnGap: 8
    },
    buttons: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        rowGap: 8
    },
    iconButton: {
        position: "relative",
        flexShrink: 0,
        borderStyle: "solid",
        backgroundColor: "rgba(42, 48, 56, 1)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        columnGap: 0,
        padding: 8,
        borderWidth: 1,
        borderColor: "rgba(56, 62, 70, 1)",
        borderRadius: 4
    },
    nav_Crop: {
        position: "relative",
        flexShrink: 0,
        height: 24,
        width: 24,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0,
        borderRadius: 4
    },
    __vector: {
        position: "absolute",
        flexShrink: 0,
        top: 4,
        right: 4,
        bottom: 4,
        left: 4,
        overflow: "visible"
    },
    _iconButton: {
        position: "relative",
        flexShrink: 0,
        borderStyle: "solid",
        backgroundColor: "rgba(42, 48, 56, 1)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        columnGap: 0,
        padding: 8,
        borderWidth: 1,
        borderColor: "rgba(56, 62, 70, 1)",
        borderRadius: 4
    },
    diff: {
        position: "relative",
        flexShrink: 0,
        height: 24,
        width: 24,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0,
        borderRadius: 4
    },
    ___vector: {
        position: "absolute",
        flexShrink: 0,
        top: 3,
        right: 5,
        bottom: 3,
        left: 5,
        overflow: "visible"
    },
    __iconButton: {
        position: "relative",
        flexShrink: 0,
        borderStyle: "solid",
        backgroundColor: "rgba(42, 48, 56, 1)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        columnGap: 0,
        padding: 8,
        borderWidth: 1,
        borderColor: "rgba(56, 62, 70, 1)",
        borderRadius: 4
    },
    download: {
        position: "relative",
        flexShrink: 0,
        height: 24,
        width: 24,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0
    },
    ____vector: {
        position: "absolute",
        flexShrink: 0,
        top: 6,
        right: 7,
        bottom: 6,
        left: 7,
        overflow: "visible"
    },
    generatedimageVideo: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        width: 656,
        paddingTop: 250,
        paddingBottom: 0,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        rowGap: 20,
        paddingHorizontal: 0,
        borderRadius: 4
    },
    rectangle1624: {
        position: "absolute",
        flexShrink: 0,
        width: 656,
        height: 655
    },
    _buttons: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        rowGap: 8
    },
    toggleGroupVertical: {
        position: "relative",
        flexShrink: 0,
        borderStyle: "solid",
        backgroundColor: "rgba(42, 48, 56, 1)",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        rowGap: 0,
        borderWidth: 1,
        borderColor: "rgba(56, 62, 70, 1)",
        borderRadius: 4
    },
    buttonToggleIconSlotVertical: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        borderTopLeftRadius: 4,
        borderTopRightRadius: 4,
        borderBottomRightRadius: 0,
        borderBottomLeftRadius: 0,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        columnGap: 0,
        padding: 8
    },
    magic_Wand: {
        position: "relative",
        flexShrink: 0,
        height: 24,
        width: 24,
        transform: "rotateZ(-0.00deg)",
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0,
        borderRadius: 4
    },
    _____vector: {
        position: "absolute",
        flexShrink: 0,
        top: 2,
        right: 3,
        bottom: 3,
        left: 3,
        overflow: "visible"
    },
    divH: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        height: 1,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0
    },
    rectangle1064: {
        position: "absolute",
        flexShrink: 0,
        top: 0,
        right: 0,
        bottom: 0,
        left: 0,
        backgroundColor: "rgba(56, 62, 70, 1)",
        borderRadius: 1
    },
    _buttonToggleIconSlotVertical: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        borderTopLeftRadius: 0,
        borderTopRightRadius: 0,
        borderBottomRightRadius: 4,
        borderBottomLeftRadius: 4,
        backgroundColor: "rgba(120, 174, 255, 1)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        columnGap: 0,
        padding: 8
    },
    paint: {
        position: "relative",
        flexShrink: 0,
        height: 24,
        width: 24,
        transform: "rotateZ(0.00deg)",
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0,
        borderRadius: 4
    },
    ______vector: {
        position: "absolute",
        flexShrink: 0,
        top: 3,
        right: 3,
        bottom: 3,
        left: 3,
        overflow: "visible"
    },
    slider: {
        position: "relative",
        flexShrink: 0,
        width: 40,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        rowGap: 0
    },
    size: {
        position: "relative",
        flexShrink: 0,
        height: 30,
        width: 32,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        rowGap: 0
    },
    ellipse2: {
        position: "absolute",
        flexShrink: 0,
        top: 7,
        right: 8,
        bottom: 7,
        left: 9,
        overflow: "visible"
    },
    frame1419: {
        position: "relative",
        flexShrink: 0,
        height: 160,
        width: 18,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0
    },
    line1Stroke: {
        position: "absolute",
        flexShrink: 0,
        top: 78,
        right: -71,
        left: -71,
        height: 4,
        overflow: "visible"
    },
    rectangle16: {
        position: "absolute",
        flexShrink: 0,
        top: -5,
        right: 5,
        left: 5,
        height: 18,
        transform: "rotateZ(90.00deg)",
        borderStyle: "solid",
        backgroundColor: "rgba(120, 174, 255, 1)",
        borderWidth: 1,
        borderColor: "rgba(56, 62, 70, 1)",
        borderRadius: 6
    },
    _size: {
        position: "relative",
        flexShrink: 0,
        height: 30,
        width: 32,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        rowGap: 0
    },
    _ellipse2: {
        position: "absolute",
        flexShrink: 0,
        top: 12,
        right: 13,
        bottom: 12,
        left: 13,
        overflow: "visible"
    },
    controls: {
        position: "relative",
        flexShrink: 0,
        width: 1204,
        backgroundColor: "rgba(42, 48, 56, 1)",
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 8,
        padding: 12,
        borderRadius: 4
    },
    frame1410: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        display: "flex",
        alignItems: "flex-end",
        columnGap: 8
    },
    textField: {
        position: "relative",
        alignSelf: "stretch",
        flexGrow: 1,
        flexShrink: 1,
        flexBasis: 0,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0
    },
    _label: {
        position: "relative",
        flexShrink: 0,
        height: 25,
        width: 400,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0
    },
    __label: {
        position: "absolute",
        flexShrink: 0,
        width: 42,
        height: 16,
        textAlign: "left",
        color: "rgba(200, 205, 213, 1)",
        fontFamily: "Amazon Ember",
        fontSize: 12,
        fontWeight: 500,
        lineHeight: "16px"
    },
    field: {
        position: "relative",
        alignSelf: "stretch",
        flexGrow: 1,
        flexShrink: 1,
        flexBasis: 0,
        borderStyle: "solid",
        backgroundColor: "rgba(42, 48, 56, 1)",
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 10,
        paddingHorizontal: 16,
        paddingVertical: 10,
        borderWidth: 1,
        borderColor: "rgba(56, 62, 70, 1)",
        borderRadius: 4
    },
    input: {
        position: "relative",
        alignSelf: "stretch",
        flexGrow: 1,
        flexShrink: 1,
        flexBasis: 0,
        display: "flex",
        alignItems: "flex-start",
        columnGap: 1
    },
    _____text: {
        position: "relative",
        flexGrow: 1,
        flexShrink: 1,
        flexBasis: 0,
        textAlign: "left",
        color: "rgba(255, 255, 255, 1)",
        fontFamily: "Amazon Ember",
        fontSize: 14,
        fontWeight: 400,
        lineHeight: "20px"
    },
    frame1426: {
        position: "relative",
        flexShrink: 0,
        width: 66,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 8
    },
    ___label: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        textAlign: "left",
        color: "rgba(200, 205, 213, 1)",
        fontFamily: "Amazon Ember",
        fontSize: 12,
        fontWeight: 500,
        lineHeight: "16px"
    },
    ___iconButton: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        height: 60,
        borderStyle: "solid",
        backgroundColor: "rgba(42, 48, 56, 1)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        columnGap: 0,
        padding: 8,
        borderWidth: 1,
        borderColor: "rgba(56, 62, 70, 1)",
        borderRadius: 4
    },
    image_Add: {
        position: "relative",
        flexShrink: 0,
        height: 24,
        width: 24,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0
    },
    _______vector: {
        position: "absolute",
        flexShrink: 0,
        top: 4,
        right: 4,
        bottom: 5,
        left: 5,
        overflow: "visible"
    },
    frame1411: {
        position: "relative",
        flexShrink: 0,
        display: "flex",
        alignItems: "flex-end",
        columnGap: 4
    },
    formField: {
        position: "relative",
        flexShrink: 0,
        width: 100,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0
    },
    _field: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        height: 40,
        paddingLeft: 16,
        paddingRight: 4,
        borderStyle: "solid",
        backgroundColor: "rgba(42, 48, 56, 1)",
        display: "flex",
        alignItems: "center",
        columnGap: 0,
        paddingVertical: 10,
        borderWidth: 1,
        borderColor: "rgba(56, 62, 70, 1)",
        borderRadius: 4
    },
    _input: {
        position: "relative",
        flexGrow: 1,
        flexShrink: 1,
        flexBasis: 0,
        display: "flex",
        alignItems: "center",
        columnGap: 1
    },
    ______text: {
        position: "relative",
        flexGrow: 1,
        flexShrink: 1,
        flexBasis: 0,
        textAlign: "left",
        color: "rgba(255, 255, 255, 1)",
        fontFamily: "Amazon Ember",
        fontSize: 14,
        fontWeight: 400,
        lineHeight: "20px"
    },
    randomize: {
        position: "relative",
        flexShrink: 0,
        height: 24,
        width: 24,
        transform: "rotateZ(-0.00deg)",
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0,
        borderRadius: 4
    },
    ________vector: {
        position: "absolute",
        flexShrink: 0,
        top: 4,
        right: 4,
        bottom: 4,
        left: 4,
        overflow: "visible"
    },
    __buttons: {
        position: "relative",
        flexShrink: 0,
        display: "flex",
        alignItems: "center",
        columnGap: 8
    },
    _button: {
        position: "relative",
        flexShrink: 0,
        height: 40,
        width: 160,
        backgroundColor: "rgba(255, 153, 0, 1)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        columnGap: 4,
        paddingHorizontal: 16,
        paddingVertical: 10,
        borderRadius: 4
    },
    nav_GenAI: {
        position: "relative",
        flexShrink: 0,
        height: 24,
        width: 24,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0,
        borderRadius: 4
    },
    _________vector: {
        position: "absolute",
        flexShrink: 0,
        top: 4,
        right: 4,
        bottom: 4,
        left: 4,
        overflow: "visible"
    },
    ____label: {
        position: "relative",
        flexShrink: 0,
        textAlign: "center",
        color: "rgba(13, 17, 20, 1)",
        fontFamily: "Open Sans",
        fontSize: 14,
        fontWeight: 600,
        lineHeight: "20px"
    },
    __button: {
        position: "relative",
        flexShrink: 0,
        height: 40,
        width: 160,
        backgroundColor: "rgba(255, 153, 0, 1)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        columnGap: 4,
        paddingHorizontal: 16,
        paddingVertical: 10,
        borderRadius: 4
    },
    video_AI: {
        position: "relative",
        flexShrink: 0,
        height: 24,
        width: 24,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0,
        borderRadius: 4
    },
    __________vector: {
        position: "absolute",
        flexShrink: 0,
        top: 4,
        right: 4,
        bottom: 4,
        left: 4,
        overflow: "visible"
    },
    _____label: {
        position: "relative",
        flexShrink: 0,
        textAlign: "center",
        color: "rgba(13, 17, 20, 1)",
        fontFamily: "Open Sans",
        fontSize: 14,
        fontWeight: 600,
        lineHeight: "20px"
    },
    advanced: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0
    },
    _advanced: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        display: "flex",
        alignItems: "center",
        columnGap: 0
    },
    __advanced: {
        position: "relative",
        flexShrink: 0,
        textAlign: "left",
        color: "rgba(200, 205, 213, 1)",
        fontFamily: "Amazon Ember",
        fontSize: 12,
        fontWeight: 500,
        lineHeight: "16px"
    },
    directional_Filled_Large: {
        position: "relative",
        flexShrink: 0,
        height: 24,
        width: 24,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0,
        borderRadius: 4
    },
    ___________vector: {
        position: "absolute",
        flexShrink: 0,
        top: 7,
        right: 8,
        bottom: 7,
        left: 8,
        overflow: "visible"
    },
    _divH: {
        position: "relative",
        flexGrow: 1,
        flexShrink: 1,
        flexBasis: 0,
        height: 1102,
        transform: "rotateZ(90.00deg)",
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0
    },
    _rectangle1064: {
        position: "absolute",
        flexShrink: 0,
        top: 0,
        right: 0,
        bottom: 0,
        left: 0,
        backgroundColor: "rgba(56, 62, 70, 1)",
        borderRadius: 1
    },
    gallery: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        width: 296,
        backgroundColor: "rgba(42, 48, 56, 1)",
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 16,
        padding: 12,
        borderRadius: 4
    },
    ___title: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        display: "flex",
        alignItems: "baseline",
        columnGap: 4
    },
    restyledmedia: {
        position: "relative",
        flexGrow: 1,
        flexShrink: 1,
        flexBasis: 0,
        textAlign: "left",
        color: "rgba(255, 255, 255, 1)",
        fontFamily: "Amazon Ember",
        fontSize: 24,
        fontWeight: 300,
        lineHeight: "32px"
    },
    secondaryTabsGroup: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        borderTopWidth: 0,
        borderRightWidth: 0,
        borderBottomWidth: 1,
        borderLeftWidth: 0,
        borderStyle: "solid",
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0,
        borderColor: "rgba(56, 62, 70, 1)"
    },
    tabs: {
        position: "relative",
        flexShrink: 0,
        display: "flex",
        alignItems: "flex-start",
        columnGap: 24,
        paddingHorizontal: 16,
        paddingVertical: 0
    },
    secondaryTabs: {
        position: "relative",
        flexShrink: 0,
        paddingTop: 8,
        paddingBottom: 0,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        rowGap: 24,
        paddingHorizontal: 0
    },
    ______label: {
        position: "relative",
        flexShrink: 0,
        textAlign: "left",
        color: "rgba(255, 255, 255, 1)",
        fontFamily: "Amazon Ember",
        fontSize: 14,
        fontWeight: 700,
        lineHeight: "20px"
    },
    underline: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        height: 2,
        borderTopLeftRadius: 4,
        borderTopRightRadius: 4,
        borderBottomRightRadius: 0,
        borderBottomLeftRadius: 0,
        backgroundColor: "rgba(255, 255, 255, 1)"
    },
    _secondaryTabs: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        width: 36,
        paddingTop: 6,
        paddingBottom: 24,
        display: "flex",
        alignItems: "center",
        columnGap: 10,
        paddingHorizontal: 0
    },
    _______label: {
        position: "relative",
        flexShrink: 0,
        textAlign: "left",
        color: "rgba(200, 205, 213, 1)",
        fontFamily: "Amazon Ember",
        fontSize: 14,
        fontWeight: 700,
        lineHeight: "20px"
    },
    __secondaryTabs: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        paddingTop: 6,
        paddingBottom: 24,
        display: "flex",
        alignItems: "center",
        columnGap: 10,
        paddingHorizontal: 0
    },
    ________label: {
        position: "relative",
        flexShrink: 0,
        textAlign: "left",
        color: "rgba(200, 205, 213, 1)",
        fontFamily: "Amazon Ember",
        fontSize: 14,
        fontWeight: 700,
        lineHeight: "20px"
    },
    _container: {
        position: "relative",
        alignSelf: "stretch",
        flexShrink: 0,
        display: "flex",
        flexWrap: "wrap",
        alignItems: "center",
        columnGap: 0
    },
    imageCard03: {
        position: "relative",
        flexShrink: 0,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        rowGap: 0,
        padding: 4,
        borderRadius: 6
    },
    defaultImage: {
        position: "relative",
        flexShrink: 0,
        height: 60,
        width: 60,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0,
        borderRadius: 4
    },
    ___rectangle1532: {
        position: "absolute",
        flexShrink: 0,
        top: 0,
        right: 0,
        bottom: 0,
        left: 0
    },
    _imageCard03: {
        position: "relative",
        flexShrink: 0,
        borderStyle: "solid",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        rowGap: 0,
        padding: 4,
        borderWidth: 2,
        borderColor: "rgba(120, 174, 255, 1)",
        borderRadius: 6
    },
    _defaultImage: {
        position: "relative",
        flexShrink: 0,
        height: 60,
        width: 60,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0,
        borderRadius: 4
    },
    ____rectangle1532: {
        position: "absolute",
        flexShrink: 0,
        top: 0,
        right: 0,
        bottom: 0,
        left: 0
    },
    __imageCard03: {
        position: "relative",
        flexShrink: 0,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        rowGap: 0,
        padding: 4,
        borderRadius: 6
    },
    __defaultImage: {
        position: "relative",
        flexShrink: 0,
        height: 60,
        width: 60,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0,
        borderRadius: 4
    },
    _____rectangle1532: {
        position: "absolute",
        flexShrink: 0,
        top: 0,
        right: 0,
        bottom: 0,
        left: 0
    },
    ___imageCard03: {
        position: "relative",
        flexShrink: 0,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        rowGap: 0,
        padding: 4,
        borderRadius: 6
    },
    ___defaultImage: {
        position: "relative",
        flexShrink: 0,
        height: 60,
        width: 60,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        rowGap: 0,
        borderRadius: 4
    },
    ______rectangle1532: {
        position: "absolute",
        flexShrink: 0,
        top: 0,
        right: 0,
        bottom: 0,
        left: 0
    }
});
